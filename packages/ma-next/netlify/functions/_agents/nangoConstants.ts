// Auto-generated by scripts/nangoIntrospection.ts on 2025-05-31T07:04:20.756Z
// LLM, AGENTS, ___NEVER____ _____EVER_____ UPDATE THIS FILE UNDER ANY CIRCUMSTANCES

import { z } from 'zod';

export const SYNC_OUTPUTS_STRING = `
Supported Sync Outputs (Provider, Sync, Model, Description):
- slack,messages,SlackSyncMessage,Syncs messages from all channels the user can access, including replies.
- google-calendar,events-fork,GoogleCalendarEvent,Syncs Google Calendar events from the user's primary calendar
- google-mail,emails-fork,GmailEmail,Fetches a list of emails from Gmail. Defaults to 1-day backfill,
- dropbox,files-fork,DropboxFile,Fetches files metadata from Dropbox.
- google-drive,documents-fork,Document,Fetches documents metadata from Google Drive.
`;

export const ACTION_INPUTS_STRING = `
Supported Action Inputs (Provider, Action, Model, Description):
- harvest,add-historical-time-entry,HarvestAddHistoricalTimeEntryInput,"Adds a completed time entry for a specific duration or start/end time. Checks company settings for duration vs timestamp tracking."
- harvest,create-client,HarvestCreateClientInput,"Creates a new client in Harvest."
- harvest,create-project,HarvestCreateProjectInput,"Creates a new project in Harvest."
- harvest,delete-project,HarvestProjectInput,"Deletes a project in Harvest."
- harvest,delete-time-entry,HarvestTimeEntryInput,"Deletes a time entry in Harvest."
- harvest,get-client,HarvestClientInput,"Gets a specific client by ID."
- harvest,get-project,HarvestProjectInput,"Gets a specific project by ID."
- harvest,get-time-entry,HarvestTimeEntryInput,"Gets a specific time entry by ID."
- harvest,list-clients,Unknown,"Lists clients from Harvest."
- harvest,list-projects,HarvestProjectsInput,"Lists projects from Harvest."
- harvest,list-project-tasks,HarvestProjectTasksInput,"Lists task assignments for a specific project in Harvest."
- harvest,list-tasks,HarvestTasksInput,"Lists tasks from Harvest."
- harvest,list-time-entries,HarvestTimeEntriesInput,"Lists time entries from Harvest."
- harvest,restart-timer,HarvestTimeEntryInput,"Restarts a stopped time entry in Harvest."
- harvest,start-timer,HarvestStartTimerInput,"Starts a new timer for a task. Checks company settings for duration vs timestamp tracking."
- harvest,stop-timer,HarvestTimeEntryInput,"Stops a running time entry in Harvest."
- harvest,update-time-entry,HarvestUpdateTimeEntryInput,"Updates an existing time entry in Harvest."
- github,add-pull-request-review-comment,GithubAddPullRequestReviewCommentInput,"Add a review comment to a pull request."
- github,create-issue,GithubCreateIssueInput,"Creates a new issue in a repository."
- github,create-organization-repository,GithubCreateOrganizationRepositoryInput,"Creates a new repository within a specified organization."
- github,create-pull-request,GithubCreatePullRequestInput,"Create a new pull request in a GitHub repository."
- github,create-pull-request-review,GithubCreatePullRequestReviewInput,"Submit a review on a pull request."
- github,create-repository,GithubCreateRepositoryInput,"Creates a new repository for the authenticated user. After successful creation, describe to the user how they can push to it inluding ssh url."
- github,delete-repository,GithubRepositoryInput,"Deletes a repository."
- github,get-issue,GithubIssueInput,"Gets a specific issue by number."
- github,get-pull-request,GithubPullRequestInput,"Get details of a specific pull request in a GitHub repository."
- github,get-pull-request-comments,GithubPullRequestInput,"Get the review comments on a pull request."
- github,get-pull-request-files,GithubPullRequestInput,"Get the files changed in a specific pull request."
- github,get-pull-request-status,GithubPullRequestInput,"Get the combined status of all status checks for a pull request."
- github,get-repository,GithubRepositoryInput,"Gets a specific repository by owner and name."
- github,list-branches,GithubListBranchesInput,"List branches in a repository."
- github,list-issues,GithubIssuesInput,"Lists issues for a repository."
- github,list-pull-requests,GithubListPullRequestsInput,"List pull requests in a GitHub repository."
- github,list-repositories,Unknown,"Lists repositories for the authenticated user."
- github,merge-pull-request,GithubMergePullRequestInput,"Merge a pull request in a GitHub repository."
- github,update-issue,GithubUpdateIssueInput,"Updates an existing issue."
- github,update-pull-request,GithubUpdatePullRequestInput,"Update an existing pull request in a GitHub repository."
- github,update-pull-request-branch,GithubUpdatePullRequestBranchInput,"Update the branch of a pull request with the latest changes from the base branch."
- github,update-repository,GithubUpdateRepositoryInput,"Updates an existing repository."
- github,write-file,GithubWriteFileInput,"Write content to a particular github file within a repo. If"
- slack,add-reaction-as-user,SlackAddReactionInput,"Adds an emoji reaction to a message as the authenticated user."
- slack,get-channel-history,SlackGetChannelHistoryInput,"Retrieves message history for a specific channel."
- slack,get-message-permalink,SlackGetPermalinkInput,"Retrieves a permalink for a specific message."
- slack,get-user-info,SlackGetUserInfoInput,"Retrieves information about a specific user."
- slack,list-channels,SlackListChannelsInput,"Lists channels in Slack."
- slack,search-messages,SlackSearchMessagesInput,"Searches for messages matching a query."
- slack,send-message-as-user,SlackSendMessageInput,"Sends a message to a Slack channel as the authenticated user."
- slack,update-message-as-user,SlackUpdateMessageInput,"Updates an existing message in a channel as the authenticated user."
- google-calendar,create-event,GoogleCalendarEventInput,"Creates a new event in Google Calendar. Can either be full-day or time-based."
- google-calendar,delete-event,GoogleCalendarEventDeleteInput,"Deletes an event from Google Calendar."
- google-calendar,list-calendars,Unknown,"Lists all calendars available to the authenticated user."
- google-calendar,list-events,GoogleCalendarEventsInput,"Lists events from a specified calendar. By default will only include future events. To include past events, set the timeMin to some time in the past."
- google-calendar,update-event,GoogleCalendarEventUpdateInput,"Updates an event in Google Calendar."
- google-mail,compose-draft,GmailDraftInput,"Creates a new draft email in Gmail."
- google-mail,compose-draft-reply,GmailReplyDraftInput,"Creates a new draft email that is a reply to an existing email."
- google-mail,delete-message,GmailMessageIdInput,"Permanently deletes the specified message. Bypasses Trash."
- google-mail,get-message,GmailGetMessageInput,"Retrieves a specific email by ID."
- google-mail,list-messages,GmailListMessagesInput,"Lists emails from Gmail inbox with optional filtering."
- google-mail,modify-message-labels,GmailModifyMessageLabelsInput,"Modifies the labels applied to a specific message."
- google-mail,send-email,GmailSendEmailInput,"Sends an email via Gmail."
- google-mail,trash-message,GmailMessageIdInput,"Moves the specified message to the trash."
- google-mail,untrash-message,GmailMessageIdInput,"Removes the specified message from the trash."
- dropbox,copy-file,DropboxCopyInput,"Copy a file or folder to a different location in Dropbox and return metadata of the copied item"
- dropbox,create-folder,DropboxCreateFolderInput,"Create a new folder in Dropbox and return folder metadata"
- dropbox,delete-file,DropboxDeleteInput,"Delete a file or folder in Dropbox and return metadata of the deleted item"
- dropbox,fetch-file,Anonymous_dropbox_action_fetchfile_input,"Fetches the content of a file given its ID, processes the data using a response stream, and encodes it into a base64 string. This base64-encoded string can be used to recreate the file in its original format using an external tool."
- dropbox,get-file,DropboxGetFileInput,"Get file metadata and a download URL (not the actual file content)"
- dropbox,list-files,DropboxListFilesInput,"List files and folders in a Dropbox directory"
- dropbox,move-file,DropboxMoveInput,"Move a file or folder to a different location in Dropbox and return metadata of the moved item"
- dropbox,search-files,DropboxSearchInput,"Search for files and folders in Dropbox by filename or content"
- dropbox,upload-file,DropboxUploadFileInput,"Upload text or binary content as a file to Dropbox"
- notion,create-database,NotionCreateDatabaseInput,"Creates a new Notion database as a subpage of a specified page."
- notion,create-page,NotionCreatePageInput,"Creates a new Notion page."
- notion,get-database,NotionGetDatabaseInput,"Retrieves a specific Notion Database object by its ID."
- notion,get-page,NotionGetPageInput,"Retrieves a specific Notion Page object by its ID."
- notion,query-database,NotionQueryDatabaseInput,"Queries a Notion database for pages, with optional filters and sorts."
- notion,search,NotionSearchInput,"Searches pages and databases in Notion. IMPORTANT - Use \"\" to search for everything."
- notion,update-database,NotionUpdateDatabaseInput,"Updates properties of an existing Notion database. ALSO USED TO \"delete\" a database, set archive to true."
- notion,update-page,NotionUpdatePageInput,"Updates properties of an existing Notion page. ALSO USED TO \"delete\" a page, set archive to true."
- google-docs,create-document,GoogleDocsCreateDocumentInput,"Creates a blank Google Document."
- google-docs,get-document,GoogleDocsGetDocumentInput,"Retrieves a specific Google Document."
- google-docs,update-document,GoogleDocsUpdateDocumentInput,"Applies batch updates to a Google Document."
- linear,create-issue,LinearCreateIssueInput,"Creates a new issue in Linear."
- linear,create-project,LinearCreateProjectInput,"Creates a new project in Linear."
- linear,delete-issue,LinearIssueInput,"Deletes an issue in Linear."
- linear,fetch-models,Unknown,"Introspection endpoint to fetch the models available"
- linear,get-issue,LinearIssueInput,"Gets a specific issue by ID."
- linear,get-project,LinearProjectInput,"Gets a specific project by ID."
- linear,get-team,LinearTeamInput,"Gets a specific team by ID."
- linear,list-issues,LinearIssuesInput,"Lists issues from Linear."
- linear,list-projects,LinearProjectsInput,"List all projects from Linear"
- linear,list-teams,LinearTeamsInput,"Lists teams from Linear."
- linear,update-issue,LinearUpdateIssueInput,"Updates an existing issue in Linear."
- linear,update-project,LinearUpdateProjectInput,"Updates an existing project in Linear."
- google-sheet,create-sheet,GoogleSheetCreateInput,"Creates a new Google Sheet with optional initial data."
- google-sheet,fetch-spreadsheet,SpreadsheetId,"Fetches the content of a spreadsheet given its ID."
- google-sheet,update-sheet,GoogleSheetUpdateInput,"Updates an existing Google Sheet with new data."
- google-drive,fetch-document,IdEntity,"Fetches the content of a file given its ID, processes the data using"
- google-drive,fetch-google-doc,IdEntity,"Fetches the content of a native google document given its ID. Outputs"
- google-drive,fetch-google-sheet,IdEntity,"Fetches the content of a native google spreadsheet given its ID. Outputs"
- google-drive,folder-content,FolderContentInput,"Fetches the top-level content (files and folders) of a folder given its ID."
- google-drive,list-documents,ListDocumentsInput,"Lists documents in Google Drive with optional filtering by folder ID and document type."
- google-drive,list-root-folders,Unknown,"Lists folders at the root level of Google Drive."
- google-drive,upload-document,UploadFileInput,"Uploads a file to Google Drive. The file is uploaded to the root directory"
- linkedin,get-user-profile,Unknown,"Gets the authenticated user's profile information from LinkedIn."
- linkedin,send-post,LinkedInPostInput,"Creates a new post on LinkedIn."
- twitter-v2,get-user-profile,Unknown,"Gets the authenticated user's profile information from X."
- twitter-v2,send-post,XSocialPostInput,"Sends a new post to X."
`;

export const ACTION_OUTPUTS_STRING = `
Supported Action Outputs (Provider, Action, Model):
- harvest,add-historical-time-entry,HarvestTimeEntry
- harvest,create-client,HarvestClient
- harvest,create-project,HarvestProject
- harvest,delete-project,HarvestDeleteProjectOutput
- harvest,delete-time-entry,HarvestDeleteTimeEntryOutput
- harvest,get-client,HarvestClient
- harvest,get-project,HarvestProject
- harvest,get-time-entry,HarvestTimeEntry
- harvest,list-clients,HarvestClientList
- harvest,list-projects,HarvestProjectList
- harvest,list-project-tasks,HarvestProjectTaskList
- harvest,list-tasks,HarvestTaskList
- harvest,list-time-entries,HarvestTimeEntryList
- harvest,restart-timer,HarvestTimeEntry
- harvest,start-timer,HarvestTimeEntry
- harvest,stop-timer,HarvestTimeEntry
- harvest,update-time-entry,HarvestTimeEntry
- github,add-pull-request-review-comment,GithubPullRequestComment
- github,create-issue,GithubIssue
- github,create-organization-repository,GithubRepository
- github,create-pull-request,GithubPullRequest
- github,create-pull-request-review,GithubPullRequestReview
- github,create-repository,GithubRepository
- github,delete-repository,GithubDeleteRepositoryOutput
- github,get-issue,GithubIssue
- github,get-pull-request,GithubPullRequest
- github,get-pull-request-comments,GithubPullRequestCommentList
- github,get-pull-request-files,GithubPullRequestFileList
- github,get-pull-request-status,GithubCombinedStatus
- github,get-repository,GithubRepository
- github,list-branches,GithubBranchList
- github,list-issues,GithubIssueList
- github,list-pull-requests,GithubPullRequestList
- github,list-repositories,GithubRepositoryList
- github,merge-pull-request,GithubMergeResult
- github,update-issue,GithubIssue
- github,update-pull-request,GithubPullRequest
- github,update-pull-request-branch,GithubBranchUpdateResult
- github,update-repository,GithubRepository
- github,write-file,GithubWriteFileActionResult
- slack,add-reaction-as-user,SlackReactionOutput
- slack,get-channel-history,SlackMessageList
- slack,get-message-permalink,SlackPermalinkOutput
- slack,get-user-info,SlackUserInfo
- slack,list-channels,SlackConversationsList
- slack,search-messages,SlackSearchResultList
- slack,send-message-as-user,SlackSendMessageOutput
- slack,update-message-as-user,SlackUpdateMessageOutput
- google-calendar,create-event,GoogleCalendarEvent
- google-calendar,delete-event,GoogleCalendarEventDeleteOutput
- google-calendar,list-calendars,GoogleCalendarList
- google-calendar,list-events,GoogleCalendarEventList
- google-calendar,update-event,GoogleCalendarEvent
- google-mail,compose-draft,GmailDraftOutput
- google-mail,compose-draft-reply,GmailReplyDraftOutput
- google-mail,delete-message,GmailDeleteMessageOutput
- google-mail,get-message,GmailMessage
- google-mail,list-messages,GmailMessageList
- google-mail,modify-message-labels,GmailMessage
- google-mail,send-email,GmailSendEmailOutput
- google-mail,trash-message,GmailMessage
- google-mail,untrash-message,GmailMessage
- dropbox,copy-file,DropboxEntry
- dropbox,create-folder,DropboxFolder
- dropbox,delete-file,DropboxDeleteResult
- dropbox,fetch-file,Anonymous_dropbox_action_fetchfile_output
- dropbox,get-file,DropboxFile
- dropbox,list-files,DropboxFileList
- dropbox,move-file,DropboxEntry
- dropbox,search-files,DropboxSearchResult
- dropbox,upload-file,DropboxFile
- notion,create-database,NotionDatabase
- notion,create-page,NotionPageOrDatabase
- notion,get-database,NotionDatabase
- notion,get-page,NotionPageOrDatabase
- notion,query-database,NotionQueryDatabaseOutput
- notion,search,NotionSearchOutput
- notion,update-database,NotionDatabase
- notion,update-page,NotionPageOrDatabase
- google-docs,create-document,GoogleDocsDocument
- google-docs,get-document,GoogleDocsDocument
- google-docs,update-document,GoogleDocsUpdateDocumentOutput
- linear,create-issue,LinearIssue
- linear,create-project,LinearProject
- linear,delete-issue,LinearDeleteIssueOutput
- linear,fetch-models,ModelResponse
- linear,get-issue,LinearIssue
- linear,get-project,LinearProject
- linear,get-team,LinearTeam
- linear,list-issues,LinearIssueList
- linear,list-projects,LinearProjectList
- linear,list-teams,LinearTeamList
- linear,update-issue,LinearIssue
- linear,update-project,LinearProject
- google-sheet,create-sheet,GoogleSheetCreateOutput
- google-sheet,fetch-spreadsheet,Spreadsheet
- google-sheet,update-sheet,GoogleSheetUpdateOutput
- google-drive,fetch-document,Anonymous_googledrive_action_fetchdocument_output
- google-drive,fetch-google-doc,JSONDocument
- google-drive,fetch-google-sheet,JSONSpreadsheet
- google-drive,folder-content,FolderContent
- google-drive,list-documents,GoogleDriveDocumentList
- google-drive,list-root-folders,GoogleDriveFolderList
- google-drive,upload-document,GoogleDocument
- linkedin,get-user-profile,LinkedInUserProfile
- linkedin,send-post,LinkedInPostOutput
- twitter-v2,get-user-profile,XSocialUserProfile
- twitter-v2,send-post,XSocialPostOutput
`;

export const SYNC_OUTPUTS = [
  { provider: "slack", sync: "messages", model: "SlackSyncMessage", description: "Syncs messages from all channels the user can access, including replies." },
  { provider: "google-calendar", sync: "events-fork", model: "GoogleCalendarEvent", description: "Syncs Google Calendar events from the user's primary calendar" },
  { provider: "google-mail", sync: "emails-fork", model: "GmailEmail", description: "Fetches a list of emails from Gmail. Defaults to 1-day backfill," },
  { provider: "dropbox", sync: "files-fork", model: "DropboxFile", description: "Fetches files metadata from Dropbox." },
  { provider: "google-drive", sync: "documents-fork", model: "Document", description: "Fetches documents metadata from Google Drive." },
] as const;

export const ACTION_INPUTS = [
  { provider: "harvest", action: "add-historical-time-entry", model: "HarvestAddHistoricalTimeEntryInput", description: "Adds a completed time entry for a specific duration or start/end time. Checks company settings for duration vs timestamp tracking." },
  { provider: "harvest", action: "create-client", model: "HarvestCreateClientInput", description: "Creates a new client in Harvest." },
  { provider: "harvest", action: "create-project", model: "HarvestCreateProjectInput", description: "Creates a new project in Harvest." },
  { provider: "harvest", action: "delete-project", model: "HarvestProjectInput", description: "Deletes a project in Harvest." },
  { provider: "harvest", action: "delete-time-entry", model: "HarvestTimeEntryInput", description: "Deletes a time entry in Harvest." },
  { provider: "harvest", action: "get-client", model: "HarvestClientInput", description: "Gets a specific client by ID." },
  { provider: "harvest", action: "get-project", model: "HarvestProjectInput", description: "Gets a specific project by ID." },
  { provider: "harvest", action: "get-time-entry", model: "HarvestTimeEntryInput", description: "Gets a specific time entry by ID." },
  { provider: "harvest", action: "list-clients", model: null, description: "Lists clients from Harvest." },
  { provider: "harvest", action: "list-projects", model: "HarvestProjectsInput", description: "Lists projects from Harvest." },
  { provider: "harvest", action: "list-project-tasks", model: "HarvestProjectTasksInput", description: "Lists task assignments for a specific project in Harvest." },
  { provider: "harvest", action: "list-tasks", model: "HarvestTasksInput", description: "Lists tasks from Harvest." },
  { provider: "harvest", action: "list-time-entries", model: "HarvestTimeEntriesInput", description: "Lists time entries from Harvest." },
  { provider: "harvest", action: "restart-timer", model: "HarvestTimeEntryInput", description: "Restarts a stopped time entry in Harvest." },
  { provider: "harvest", action: "start-timer", model: "HarvestStartTimerInput", description: "Starts a new timer for a task. Checks company settings for duration vs timestamp tracking." },
  { provider: "harvest", action: "stop-timer", model: "HarvestTimeEntryInput", description: "Stops a running time entry in Harvest." },
  { provider: "harvest", action: "update-time-entry", model: "HarvestUpdateTimeEntryInput", description: "Updates an existing time entry in Harvest." },
  { provider: "github", action: "add-pull-request-review-comment", model: "GithubAddPullRequestReviewCommentInput", description: "Add a review comment to a pull request." },
  { provider: "github", action: "create-issue", model: "GithubCreateIssueInput", description: "Creates a new issue in a repository." },
  { provider: "github", action: "create-organization-repository", model: "GithubCreateOrganizationRepositoryInput", description: "Creates a new repository within a specified organization." },
  { provider: "github", action: "create-pull-request", model: "GithubCreatePullRequestInput", description: "Create a new pull request in a GitHub repository." },
  { provider: "github", action: "create-pull-request-review", model: "GithubCreatePullRequestReviewInput", description: "Submit a review on a pull request." },
  { provider: "github", action: "create-repository", model: "GithubCreateRepositoryInput", description: "Creates a new repository for the authenticated user. After successful creation, describe to the user how they can push to it inluding ssh url." },
  { provider: "github", action: "delete-repository", model: "GithubRepositoryInput", description: "Deletes a repository." },
  { provider: "github", action: "get-issue", model: "GithubIssueInput", description: "Gets a specific issue by number." },
  { provider: "github", action: "get-pull-request", model: "GithubPullRequestInput", description: "Get details of a specific pull request in a GitHub repository." },
  { provider: "github", action: "get-pull-request-comments", model: "GithubPullRequestInput", description: "Get the review comments on a pull request." },
  { provider: "github", action: "get-pull-request-files", model: "GithubPullRequestInput", description: "Get the files changed in a specific pull request." },
  { provider: "github", action: "get-pull-request-status", model: "GithubPullRequestInput", description: "Get the combined status of all status checks for a pull request." },
  { provider: "github", action: "get-repository", model: "GithubRepositoryInput", description: "Gets a specific repository by owner and name." },
  { provider: "github", action: "list-branches", model: "GithubListBranchesInput", description: "List branches in a repository." },
  { provider: "github", action: "list-issues", model: "GithubIssuesInput", description: "Lists issues for a repository." },
  { provider: "github", action: "list-pull-requests", model: "GithubListPullRequestsInput", description: "List pull requests in a GitHub repository." },
  { provider: "github", action: "list-repositories", model: null, description: "Lists repositories for the authenticated user." },
  { provider: "github", action: "merge-pull-request", model: "GithubMergePullRequestInput", description: "Merge a pull request in a GitHub repository." },
  { provider: "github", action: "update-issue", model: "GithubUpdateIssueInput", description: "Updates an existing issue." },
  { provider: "github", action: "update-pull-request", model: "GithubUpdatePullRequestInput", description: "Update an existing pull request in a GitHub repository." },
  { provider: "github", action: "update-pull-request-branch", model: "GithubUpdatePullRequestBranchInput", description: "Update the branch of a pull request with the latest changes from the base branch." },
  { provider: "github", action: "update-repository", model: "GithubUpdateRepositoryInput", description: "Updates an existing repository." },
  { provider: "github", action: "write-file", model: "GithubWriteFileInput", description: "Write content to a particular github file within a repo. If" },
  { provider: "slack", action: "add-reaction-as-user", model: "SlackAddReactionInput", description: "Adds an emoji reaction to a message as the authenticated user." },
  { provider: "slack", action: "get-channel-history", model: "SlackGetChannelHistoryInput", description: "Retrieves message history for a specific channel." },
  { provider: "slack", action: "get-message-permalink", model: "SlackGetPermalinkInput", description: "Retrieves a permalink for a specific message." },
  { provider: "slack", action: "get-user-info", model: "SlackGetUserInfoInput", description: "Retrieves information about a specific user." },
  { provider: "slack", action: "list-channels", model: "SlackListChannelsInput", description: "Lists channels in Slack." },
  { provider: "slack", action: "search-messages", model: "SlackSearchMessagesInput", description: "Searches for messages matching a query." },
  { provider: "slack", action: "send-message-as-user", model: "SlackSendMessageInput", description: "Sends a message to a Slack channel as the authenticated user." },
  { provider: "slack", action: "update-message-as-user", model: "SlackUpdateMessageInput", description: "Updates an existing message in a channel as the authenticated user." },
  { provider: "google-calendar", action: "create-event", model: "GoogleCalendarEventInput", description: "Creates a new event in Google Calendar. Can either be full-day or time-based." },
  { provider: "google-calendar", action: "delete-event", model: "GoogleCalendarEventDeleteInput", description: "Deletes an event from Google Calendar." },
  { provider: "google-calendar", action: "list-calendars", model: null, description: "Lists all calendars available to the authenticated user." },
  { provider: "google-calendar", action: "list-events", model: "GoogleCalendarEventsInput", description: "Lists events from a specified calendar. By default will only include future events. To include past events, set the timeMin to some time in the past." },
  { provider: "google-calendar", action: "update-event", model: "GoogleCalendarEventUpdateInput", description: "Updates an event in Google Calendar." },
  { provider: "google-mail", action: "compose-draft", model: "GmailDraftInput", description: "Creates a new draft email in Gmail." },
  { provider: "google-mail", action: "compose-draft-reply", model: "GmailReplyDraftInput", description: "Creates a new draft email that is a reply to an existing email." },
  { provider: "google-mail", action: "delete-message", model: "GmailMessageIdInput", description: "Permanently deletes the specified message. Bypasses Trash." },
  { provider: "google-mail", action: "get-message", model: "GmailGetMessageInput", description: "Retrieves a specific email by ID." },
  { provider: "google-mail", action: "list-messages", model: "GmailListMessagesInput", description: "Lists emails from Gmail inbox with optional filtering." },
  { provider: "google-mail", action: "modify-message-labels", model: "GmailModifyMessageLabelsInput", description: "Modifies the labels applied to a specific message." },
  { provider: "google-mail", action: "send-email", model: "GmailSendEmailInput", description: "Sends an email via Gmail." },
  { provider: "google-mail", action: "trash-message", model: "GmailMessageIdInput", description: "Moves the specified message to the trash." },
  { provider: "google-mail", action: "untrash-message", model: "GmailMessageIdInput", description: "Removes the specified message from the trash." },
  { provider: "dropbox", action: "copy-file", model: "DropboxCopyInput", description: "Copy a file or folder to a different location in Dropbox and return metadata of the copied item" },
  { provider: "dropbox", action: "create-folder", model: "DropboxCreateFolderInput", description: "Create a new folder in Dropbox and return folder metadata" },
  { provider: "dropbox", action: "delete-file", model: "DropboxDeleteInput", description: "Delete a file or folder in Dropbox and return metadata of the deleted item" },
  { provider: "dropbox", action: "fetch-file", model: "Anonymous_dropbox_action_fetchfile_input", description: "Fetches the content of a file given its ID, processes the data using a response stream, and encodes it into a base64 string. This base64-encoded string can be used to recreate the file in its original format using an external tool." },
  { provider: "dropbox", action: "get-file", model: "DropboxGetFileInput", description: "Get file metadata and a download URL (not the actual file content)" },
  { provider: "dropbox", action: "list-files", model: "DropboxListFilesInput", description: "List files and folders in a Dropbox directory" },
  { provider: "dropbox", action: "move-file", model: "DropboxMoveInput", description: "Move a file or folder to a different location in Dropbox and return metadata of the moved item" },
  { provider: "dropbox", action: "search-files", model: "DropboxSearchInput", description: "Search for files and folders in Dropbox by filename or content" },
  { provider: "dropbox", action: "upload-file", model: "DropboxUploadFileInput", description: "Upload text or binary content as a file to Dropbox" },
  { provider: "notion", action: "create-database", model: "NotionCreateDatabaseInput", description: "Creates a new Notion database as a subpage of a specified page." },
  { provider: "notion", action: "create-page", model: "NotionCreatePageInput", description: "Creates a new Notion page." },
  { provider: "notion", action: "get-database", model: "NotionGetDatabaseInput", description: "Retrieves a specific Notion Database object by its ID." },
  { provider: "notion", action: "get-page", model: "NotionGetPageInput", description: "Retrieves a specific Notion Page object by its ID." },
  { provider: "notion", action: "query-database", model: "NotionQueryDatabaseInput", description: "Queries a Notion database for pages, with optional filters and sorts." },
  { provider: "notion", action: "search", model: "NotionSearchInput", description: "Searches pages and databases in Notion. IMPORTANT - Use \"\" to search for everything." },
  { provider: "notion", action: "update-database", model: "NotionUpdateDatabaseInput", description: "Updates properties of an existing Notion database. ALSO USED TO \"delete\" a database, set archive to true." },
  { provider: "notion", action: "update-page", model: "NotionUpdatePageInput", description: "Updates properties of an existing Notion page. ALSO USED TO \"delete\" a page, set archive to true." },
  { provider: "google-docs", action: "create-document", model: "GoogleDocsCreateDocumentInput", description: "Creates a blank Google Document." },
  { provider: "google-docs", action: "get-document", model: "GoogleDocsGetDocumentInput", description: "Retrieves a specific Google Document." },
  { provider: "google-docs", action: "update-document", model: "GoogleDocsUpdateDocumentInput", description: "Applies batch updates to a Google Document." },
  { provider: "linear", action: "create-issue", model: "LinearCreateIssueInput", description: "Creates a new issue in Linear." },
  { provider: "linear", action: "create-project", model: "LinearCreateProjectInput", description: "Creates a new project in Linear." },
  { provider: "linear", action: "delete-issue", model: "LinearIssueInput", description: "Deletes an issue in Linear." },
  { provider: "linear", action: "fetch-models", model: null, description: "Introspection endpoint to fetch the models available" },
  { provider: "linear", action: "get-issue", model: "LinearIssueInput", description: "Gets a specific issue by ID." },
  { provider: "linear", action: "get-project", model: "LinearProjectInput", description: "Gets a specific project by ID." },
  { provider: "linear", action: "get-team", model: "LinearTeamInput", description: "Gets a specific team by ID." },
  { provider: "linear", action: "list-issues", model: "LinearIssuesInput", description: "Lists issues from Linear." },
  { provider: "linear", action: "list-projects", model: "LinearProjectsInput", description: "List all projects from Linear" },
  { provider: "linear", action: "list-teams", model: "LinearTeamsInput", description: "Lists teams from Linear." },
  { provider: "linear", action: "update-issue", model: "LinearUpdateIssueInput", description: "Updates an existing issue in Linear." },
  { provider: "linear", action: "update-project", model: "LinearUpdateProjectInput", description: "Updates an existing project in Linear." },
  { provider: "google-sheet", action: "create-sheet", model: "GoogleSheetCreateInput", description: "Creates a new Google Sheet with optional initial data." },
  { provider: "google-sheet", action: "fetch-spreadsheet", model: "SpreadsheetId", description: "Fetches the content of a spreadsheet given its ID." },
  { provider: "google-sheet", action: "update-sheet", model: "GoogleSheetUpdateInput", description: "Updates an existing Google Sheet with new data." },
  { provider: "google-drive", action: "fetch-document", model: "IdEntity", description: "Fetches the content of a file given its ID, processes the data using" },
  { provider: "google-drive", action: "fetch-google-doc", model: "IdEntity", description: "Fetches the content of a native google document given its ID. Outputs" },
  { provider: "google-drive", action: "fetch-google-sheet", model: "IdEntity", description: "Fetches the content of a native google spreadsheet given its ID. Outputs" },
  { provider: "google-drive", action: "folder-content", model: "FolderContentInput", description: "Fetches the top-level content (files and folders) of a folder given its ID." },
  { provider: "google-drive", action: "list-documents", model: "ListDocumentsInput", description: "Lists documents in Google Drive with optional filtering by folder ID and document type." },
  { provider: "google-drive", action: "list-root-folders", model: null, description: "Lists folders at the root level of Google Drive." },
  { provider: "google-drive", action: "upload-document", model: "UploadFileInput", description: "Uploads a file to Google Drive. The file is uploaded to the root directory" },
  { provider: "linkedin", action: "get-user-profile", model: null, description: "Gets the authenticated user's profile information from LinkedIn." },
  { provider: "linkedin", action: "send-post", model: "LinkedInPostInput", description: "Creates a new post on LinkedIn." },
  { provider: "twitter-v2", action: "get-user-profile", model: null, description: "Gets the authenticated user's profile information from X." },
  { provider: "twitter-v2", action: "send-post", model: "XSocialPostInput", description: "Sends a new post to X." },
] as const;

export const ACTION_OUTPUTS = [
  { provider: "harvest", action: "add-historical-time-entry", model: "HarvestTimeEntry", description: "" },
  { provider: "harvest", action: "create-client", model: "HarvestClient", description: "" },
  { provider: "harvest", action: "create-project", model: "HarvestProject", description: "" },
  { provider: "harvest", action: "delete-project", model: "HarvestDeleteProjectOutput", description: "" },
  { provider: "harvest", action: "delete-time-entry", model: "HarvestDeleteTimeEntryOutput", description: "" },
  { provider: "harvest", action: "get-client", model: "HarvestClient", description: "" },
  { provider: "harvest", action: "get-project", model: "HarvestProject", description: "" },
  { provider: "harvest", action: "get-time-entry", model: "HarvestTimeEntry", description: "" },
  { provider: "harvest", action: "list-clients", model: "HarvestClientList", description: "" },
  { provider: "harvest", action: "list-projects", model: "HarvestProjectList", description: "" },
  { provider: "harvest", action: "list-project-tasks", model: "HarvestProjectTaskList", description: "" },
  { provider: "harvest", action: "list-tasks", model: "HarvestTaskList", description: "" },
  { provider: "harvest", action: "list-time-entries", model: "HarvestTimeEntryList", description: "" },
  { provider: "harvest", action: "restart-timer", model: "HarvestTimeEntry", description: "" },
  { provider: "harvest", action: "start-timer", model: "HarvestTimeEntry", description: "" },
  { provider: "harvest", action: "stop-timer", model: "HarvestTimeEntry", description: "" },
  { provider: "harvest", action: "update-time-entry", model: "HarvestTimeEntry", description: "" },
  { provider: "github", action: "add-pull-request-review-comment", model: "GithubPullRequestComment", description: "" },
  { provider: "github", action: "create-issue", model: "GithubIssue", description: "" },
  { provider: "github", action: "create-organization-repository", model: "GithubRepository", description: "" },
  { provider: "github", action: "create-pull-request", model: "GithubPullRequest", description: "" },
  { provider: "github", action: "create-pull-request-review", model: "GithubPullRequestReview", description: "" },
  { provider: "github", action: "create-repository", model: "GithubRepository", description: "" },
  { provider: "github", action: "delete-repository", model: "GithubDeleteRepositoryOutput", description: "" },
  { provider: "github", action: "get-issue", model: "GithubIssue", description: "" },
  { provider: "github", action: "get-pull-request", model: "GithubPullRequest", description: "" },
  { provider: "github", action: "get-pull-request-comments", model: "GithubPullRequestCommentList", description: "" },
  { provider: "github", action: "get-pull-request-files", model: "GithubPullRequestFileList", description: "" },
  { provider: "github", action: "get-pull-request-status", model: "GithubCombinedStatus", description: "" },
  { provider: "github", action: "get-repository", model: "GithubRepository", description: "" },
  { provider: "github", action: "list-branches", model: "GithubBranchList", description: "" },
  { provider: "github", action: "list-issues", model: "GithubIssueList", description: "" },
  { provider: "github", action: "list-pull-requests", model: "GithubPullRequestList", description: "" },
  { provider: "github", action: "list-repositories", model: "GithubRepositoryList", description: "" },
  { provider: "github", action: "merge-pull-request", model: "GithubMergeResult", description: "" },
  { provider: "github", action: "update-issue", model: "GithubIssue", description: "" },
  { provider: "github", action: "update-pull-request", model: "GithubPullRequest", description: "" },
  { provider: "github", action: "update-pull-request-branch", model: "GithubBranchUpdateResult", description: "" },
  { provider: "github", action: "update-repository", model: "GithubRepository", description: "" },
  { provider: "github", action: "write-file", model: "GithubWriteFileActionResult", description: "" },
  { provider: "slack", action: "add-reaction-as-user", model: "SlackReactionOutput", description: "" },
  { provider: "slack", action: "get-channel-history", model: "SlackMessageList", description: "" },
  { provider: "slack", action: "get-message-permalink", model: "SlackPermalinkOutput", description: "" },
  { provider: "slack", action: "get-user-info", model: "SlackUserInfo", description: "" },
  { provider: "slack", action: "list-channels", model: "SlackConversationsList", description: "" },
  { provider: "slack", action: "search-messages", model: "SlackSearchResultList", description: "" },
  { provider: "slack", action: "send-message-as-user", model: "SlackSendMessageOutput", description: "" },
  { provider: "slack", action: "update-message-as-user", model: "SlackUpdateMessageOutput", description: "" },
  { provider: "google-calendar", action: "create-event", model: "GoogleCalendarEvent", description: "" },
  { provider: "google-calendar", action: "delete-event", model: "GoogleCalendarEventDeleteOutput", description: "" },
  { provider: "google-calendar", action: "list-calendars", model: "GoogleCalendarList", description: "" },
  { provider: "google-calendar", action: "list-events", model: "GoogleCalendarEventList", description: "" },
  { provider: "google-calendar", action: "update-event", model: "GoogleCalendarEvent", description: "" },
  { provider: "google-mail", action: "compose-draft", model: "GmailDraftOutput", description: "" },
  { provider: "google-mail", action: "compose-draft-reply", model: "GmailReplyDraftOutput", description: "" },
  { provider: "google-mail", action: "delete-message", model: "GmailDeleteMessageOutput", description: "" },
  { provider: "google-mail", action: "get-message", model: "GmailMessage", description: "" },
  { provider: "google-mail", action: "list-messages", model: "GmailMessageList", description: "" },
  { provider: "google-mail", action: "modify-message-labels", model: "GmailMessage", description: "" },
  { provider: "google-mail", action: "send-email", model: "GmailSendEmailOutput", description: "" },
  { provider: "google-mail", action: "trash-message", model: "GmailMessage", description: "" },
  { provider: "google-mail", action: "untrash-message", model: "GmailMessage", description: "" },
  { provider: "dropbox", action: "copy-file", model: "DropboxEntry", description: "" },
  { provider: "dropbox", action: "create-folder", model: "DropboxFolder", description: "" },
  { provider: "dropbox", action: "delete-file", model: "DropboxDeleteResult", description: "" },
  { provider: "dropbox", action: "fetch-file", model: "Anonymous_dropbox_action_fetchfile_output", description: "" },
  { provider: "dropbox", action: "get-file", model: "DropboxFile", description: "" },
  { provider: "dropbox", action: "list-files", model: "DropboxFileList", description: "" },
  { provider: "dropbox", action: "move-file", model: "DropboxEntry", description: "" },
  { provider: "dropbox", action: "search-files", model: "DropboxSearchResult", description: "" },
  { provider: "dropbox", action: "upload-file", model: "DropboxFile", description: "" },
  { provider: "notion", action: "create-database", model: "NotionDatabase", description: "" },
  { provider: "notion", action: "create-page", model: "NotionPageOrDatabase", description: "" },
  { provider: "notion", action: "get-database", model: "NotionDatabase", description: "" },
  { provider: "notion", action: "get-page", model: "NotionPageOrDatabase", description: "" },
  { provider: "notion", action: "query-database", model: "NotionQueryDatabaseOutput", description: "" },
  { provider: "notion", action: "search", model: "NotionSearchOutput", description: "" },
  { provider: "notion", action: "update-database", model: "NotionDatabase", description: "" },
  { provider: "notion", action: "update-page", model: "NotionPageOrDatabase", description: "" },
  { provider: "google-docs", action: "create-document", model: "GoogleDocsDocument", description: "" },
  { provider: "google-docs", action: "get-document", model: "GoogleDocsDocument", description: "" },
  { provider: "google-docs", action: "update-document", model: "GoogleDocsUpdateDocumentOutput", description: "" },
  { provider: "linear", action: "create-issue", model: "LinearIssue", description: "" },
  { provider: "linear", action: "create-project", model: "LinearProject", description: "" },
  { provider: "linear", action: "delete-issue", model: "LinearDeleteIssueOutput", description: "" },
  { provider: "linear", action: "fetch-models", model: "ModelResponse", description: "" },
  { provider: "linear", action: "get-issue", model: "LinearIssue", description: "" },
  { provider: "linear", action: "get-project", model: "LinearProject", description: "" },
  { provider: "linear", action: "get-team", model: "LinearTeam", description: "" },
  { provider: "linear", action: "list-issues", model: "LinearIssueList", description: "" },
  { provider: "linear", action: "list-projects", model: "LinearProjectList", description: "" },
  { provider: "linear", action: "list-teams", model: "LinearTeamList", description: "" },
  { provider: "linear", action: "update-issue", model: "LinearIssue", description: "" },
  { provider: "linear", action: "update-project", model: "LinearProject", description: "" },
  { provider: "google-sheet", action: "create-sheet", model: "GoogleSheetCreateOutput", description: "" },
  { provider: "google-sheet", action: "fetch-spreadsheet", model: "Spreadsheet", description: "" },
  { provider: "google-sheet", action: "update-sheet", model: "GoogleSheetUpdateOutput", description: "" },
  { provider: "google-drive", action: "fetch-document", model: "Anonymous_googledrive_action_fetchdocument_output", description: "" },
  { provider: "google-drive", action: "fetch-google-doc", model: "JSONDocument", description: "" },
  { provider: "google-drive", action: "fetch-google-sheet", model: "JSONSpreadsheet", description: "" },
  { provider: "google-drive", action: "folder-content", model: "FolderContent", description: "" },
  { provider: "google-drive", action: "list-documents", model: "GoogleDriveDocumentList", description: "" },
  { provider: "google-drive", action: "list-root-folders", model: "GoogleDriveFolderList", description: "" },
  { provider: "google-drive", action: "upload-document", model: "GoogleDocument", description: "" },
  { provider: "linkedin", action: "get-user-profile", model: "LinkedInUserProfile", description: "" },
  { provider: "linkedin", action: "send-post", model: "LinkedInPostOutput", description: "" },
  { provider: "twitter-v2", action: "get-user-profile", model: "XSocialUserProfile", description: "" },
  { provider: "twitter-v2", action: "send-post", model: "XSocialPostOutput", description: "" },
] as const;

export const ACTION_INPUT_MODELS_ZOD: Record<string, z.ZodObject<any>> = {
  "HarvestAddHistoricalTimeEntryInput": z.object({
    "project_id": z.number(),
    "task_id": z.number(),
    "spent_date": z.string(),
    "hours": z.number().optional(),
    "started_time": z.string().optional(),
    "ended_time": z.string().optional(),
    "notes": z.string().optional(),
    "user_id": z.number().optional(),
    "external_reference": z.lazy(() => ACTION_INPUT_MODELS_ZOD["HarvestExternalReferenceInput"] || z.any().describe("Lazy load failed: HarvestExternalReferenceInput")).optional(),
  }),
  "HarvestExternalReferenceInput": z.object({
    "id": z.string().optional(),
    "group_id": z.string().optional(),
    "account_id": z.string().optional(),
    "permalink": z.string().optional(),
    "service": z.string().optional(),
    "service_icon_url": z.string().optional(),
  }),
  "HarvestCreateClientInput": z.object({
    "name": z.string(),
    "is_active": z.boolean().optional(),
    "address": z.string().optional(),
    "currency": z.string().optional(),
  }),
  "HarvestCreateProjectInput": z.object({
    "client_id": z.number(),
    "name": z.string(),
    "is_billable": z.boolean(),
    "bill_by": z.string(),
    "budget_by": z.string(),
    "is_fixed_fee": z.boolean().optional(),
    "fee": z.number().optional(),
    "hourly_rate": z.number().optional(),
    "budget": z.number().optional(),
    "budget_is_monthly": z.boolean().optional(),
    "notify_when_over_budget": z.boolean().optional(),
    "over_budget_notification_percentage": z.number().optional(),
    "show_budget_to_all": z.boolean().optional(),
    "cost_budget": z.number().optional(),
    "cost_budget_include_expenses": z.boolean().optional(),
    "notes": z.string().optional(),
    "starts_on": z.string().optional(),
    "ends_on": z.string().optional(),
  }),
  "HarvestProjectInput": z.object({
    "project_id": z.number(),
  }),
  "HarvestTimeEntryInput": z.object({
    "timeEntryId": z.number(),
    "id": z.number().optional(),
  }),
  "HarvestClientInput": z.object({
    "client_id": z.number(),
  }),
  "HarvestProjectsInput": z.object({
    "client_id": z.number().optional(),
    "is_active": z.boolean().optional(),
    "page": z.number().optional(),
    "per_page": z.number().optional(),
  }),
  "HarvestProjectTasksInput": z.object({
    "project_id": z.number(),
  }),
  "HarvestTasksInput": z.object({
    "is_active": z.boolean().optional(),
    "updated_since": z.string().optional(),
    "page": z.number().optional(),
    "per_page": z.number().optional(),
  }),
  "HarvestTimeEntriesInput": z.object({
    "userId": z.number().optional(),
    "clientId": z.number().optional(),
    "projectId": z.number().optional(),
    "taskId": z.number().optional(),
    "from": z.string().optional(),
    "to": z.string().optional(),
    "page": z.number().optional(),
    "perPage": z.number().optional(),
  }),
  "HarvestStartTimerInput": z.object({
    "project_id": z.number(),
    "task_id": z.number(),
    "spent_date": z.string(),
    "started_time": z.string().optional(),
    "notes": z.string().optional(),
    "user_id": z.number().optional(),
    "external_reference": z.lazy(() => ACTION_INPUT_MODELS_ZOD["HarvestExternalReferenceInput"] || z.any().describe("Lazy load failed: HarvestExternalReferenceInput")).optional(),
  }),
  "HarvestUpdateTimeEntryInput": z.object({
    "time_entry_id": z.number(),
    "project_id": z.number().optional(),
    "task_id": z.number().optional(),
    "spent_date": z.string().optional(),
    "hours": z.number().optional(),
    "started_time": z.string().optional(),
    "ended_time": z.string().optional(),
    "notes": z.string().optional(),
    "external_reference": z.lazy(() => ACTION_INPUT_MODELS_ZOD["HarvestExternalReferenceInput"] || z.any().describe("Lazy load failed: HarvestExternalReferenceInput")).optional(),
  }),
  "GithubAddPullRequestReviewCommentInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "pull_number": z.number(),
    "body": z.string(),
    "commit_id": z.string().optional(),
    "path": z.string().optional(),
    "subject_type": z.string().optional(),
    "line": z.number().optional(),
    "side": z.string().optional(),
    "start_line": z.number().optional(),
    "start_side": z.string().optional(),
    "in_reply_to": z.number().optional(),
    "diff_hunk": z.string().optional(),
  }),
  "GithubCreateIssueInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "title": z.string(),
    "body": z.string().optional(),
    "assignees": z.array(z.string()).optional(),
    "labels": z.array(z.string()).optional(),
  }),
  "GithubCreateOrganizationRepositoryInput": z.object({
    "org": z.string(),
    "name": z.string(),
    "description": z.string().optional(),
    "homepage": z.string().optional(),
    "private": z.boolean().optional(),
    "has_issues": z.boolean().optional(),
    "has_projects": z.boolean().optional(),
    "has_wiki": z.boolean().optional(),
  }),
  "GithubCreatePullRequestInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "title": z.string(),
    "body": z.string().optional(),
    "head": z.string(),
    "base": z.string(),
    "draft": z.boolean().optional(),
    "maintainer_can_modify": z.boolean().optional(),
  }),
  "GithubCreatePullRequestReviewInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "pullNumber": z.number(),
    "body": z.string().optional(),
    "event": z.string(),
    "commitId": z.string().optional(),
    "comments": z.array(z.lazy(() => ACTION_INPUT_MODELS_ZOD["GithubDraftReviewComment"] || z.any().describe("Lazy load failed: GithubDraftReviewComment"))).optional(),
  }),
  "GithubDraftReviewComment": z.object({
    "path": z.string(),
    "position": z.number().optional(),
    "line": z.number().optional(),
    "side": z.string().optional(),
    "start_line": z.number().optional(),
    "start_side": z.string().optional(),
    "body": z.string(),
  }),
  "GithubCreateRepositoryInput": z.object({
    "name": z.string(),
    "description": z.string().optional(),
    "private": z.boolean().optional(),
    "has_issues": z.boolean().optional(),
    "has_projects": z.boolean().optional(),
    "has_wiki": z.boolean().optional(),
    "auto_init": z.boolean().optional(),
    "gitignore_template": z.string().optional(),
    "license_template": z.string().optional(),
  }),
  "GithubRepositoryInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
  }),
  "GithubIssueInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "issue_number": z.number(),
  }),
  "GithubPullRequestInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "pullNumber": z.number(),
  }),
  "GithubListBranchesInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "protected": z.boolean().optional(),
    "per_page": z.number().optional(),
    "page": z.number().optional(),
  }),
  "GithubIssuesInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "state": z.string().optional(),
    "sort": z.string().optional(),
    "direction": z.string().optional(),
    "per_page": z.number().optional(),
    "page": z.number().optional(),
  }),
  "GithubListPullRequestsInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "state": z.string().optional(),
    "head": z.string().optional(),
    "base": z.string().optional(),
    "sort": z.string().optional(),
    "direction": z.string().optional(),
    "per_page": z.number().optional(),
    "page": z.number().optional(),
  }),
  "GithubMergePullRequestInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "pullNumber": z.number(),
    "commit_title": z.string().optional(),
    "commit_message": z.string().optional(),
    "merge_method": z.string().optional(),
  }),
  "GithubUpdateIssueInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "issue_number": z.number(),
    "title": z.string().optional(),
    "body": z.string().optional(),
    "state": z.string().optional(),
    "assignees": z.array(z.string()).optional(),
    "labels": z.array(z.string()).optional(),
  }),
  "GithubUpdatePullRequestInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "pullNumber": z.number(),
    "title": z.string().optional(),
    "body": z.string().optional(),
    "state": z.string().optional(),
    "base": z.string().optional(),
    "maintainer_can_modify": z.boolean().optional(),
  }),
  "GithubUpdatePullRequestBranchInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "pullNumber": z.number(),
    "expectedHeadSha": z.string().optional(),
  }),
  "GithubUpdateRepositoryInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "name": z.string().optional(),
    "description": z.string().optional(),
    "private": z.boolean().optional(),
    "has_issues": z.boolean().optional(),
    "has_projects": z.boolean().optional(),
    "has_wiki": z.boolean().optional(),
    "default_branch": z.string().optional(),
  }),
  "GithubWriteFileInput": z.object({
    "owner": z.string(),
    "repo": z.string(),
    "path": z.string(),
    "message": z.string(),
    "content": z.string(),
    "sha": z.union([z.string(), z.literal("undefined")]),
  }),
  "SlackAddReactionInput": z.object({
    "name": z.string(),
    "channel": z.string(),
    "timestamp": z.string(),
  }),
  "SlackGetChannelHistoryInput": z.object({
    "channel": z.string(),
    "limit": z.number().optional(),
    "latest": z.string().optional(),
    "oldest": z.string().optional(),
    "cursor": z.string().optional(),
  }),
  "SlackGetPermalinkInput": z.object({
    "channel": z.string(),
    "message_ts": z.string(),
  }),
  "SlackGetUserInfoInput": z.object({
    "user": z.string(),
  }),
  "SlackListChannelsInput": z.object({
    "types": z.string().optional(),
    "limit": z.number().optional(),
    "cursor": z.string().optional(),
  }),
  "SlackSearchMessagesInput": z.object({
    "query": z.string(),
    "sort": z.string().optional(),
    "sort_dir": z.string().optional(),
    "count": z.number().optional(),
    "page": z.number().optional(),
  }),
  "SlackSendMessageInput": z.object({
    "channel": z.string(),
    "text": z.string(),
    "thread_ts": z.string().optional(),
  }),
  "SlackUpdateMessageInput": z.object({
    "channel": z.string(),
    "ts": z.string(),
    "text": z.string(),
  }),
  "GoogleCalendarEventInput": z.object({
    "summary": z.string().describe("Event title / name."),
    "description": z.string().optional().describe("Contains e.g. the agenda or specifics of the meeting. Can contain HTML."),
    "location": z.string().optional().describe("Free form text."),
    "start": z.string().describe("Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter."),
    "end": z.string().describe("Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter."),
    "timeZone": z.string().optional().describe("An IANA Time Zone e.g. (Area/City)"),
    "attendees": z.array(z.string()).optional().describe("A list of attendee email addresses."),
  }),
  "GoogleCalendarEventDeleteInput": z.object({
    "calendarId": z.string().describe("Calendar identifier. Use \"primary\" unless otherwise advised."),
    "eventId": z.string().describe("Event identifier."),
    "sendUpdates": z.string().optional().describe("Whether to send notifications about the deletion of the event."),
  }),
  "GoogleCalendarEventsInput": z.object({
    "calendarId": z.string().describe("Calendar identifier. Use \"primary\" unless otherwise advised."),
    "timeMin": z.string().optional().describe("Lower bound (inclusive) for an event's end time to filter by. Defaults to now. ISO8601 string format."),
    "timeMax": z.string().optional().describe("Upper bound (exclusive) for an event's start time to filter by. Defaults to unbounded. ISO8601 string format."),
    "maxResults": z.number().optional().describe("Defaults to 250. Max 2500."),
    "pageToken": z.string().optional().describe("Token as per a previous response to get another page of results."),
    "orderBy": z.string().optional(),
    "q": z.string().optional().describe("Free text search terms to find events that match these terms."),
    "singleEvents": z.boolean().optional(),
    "timeZone": z.string().optional().describe("Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar."),
  }),
  "GoogleCalendarEventUpdateInput": z.object({
    "calendarId": z.string().describe("Calendar identifier. Use \"primary\" unless otherwise advised."),
    "eventId": z.string().describe("Event identifier."),
    "sendUpdates": z.string().optional().describe("Whether to send notifications about the event update."),
    "summary": z.string().optional().describe("Event title / name."),
    "description": z.string().optional().describe("Contains e.g. the agenda or specifics of the meeting. Can contain HTML."),
    "location": z.string().optional().describe("Free form text."),
    "start": z.string().optional().describe("Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter."),
    "end": z.string().optional().describe("Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter."),
    "timeZone": z.string().optional().describe("An IANA Time Zone e.g. (Area/City)"),
    "attendees": z.array(z.lazy(() => ACTION_INPUT_MODELS_ZOD["GoogleCalendarAttendeeInput"] || z.any().describe("Lazy load failed: GoogleCalendarAttendeeInput"))).optional().describe("A list of attendee email addresses."),
  }),
  "GoogleCalendarAttendeeInput": z.object({
    "id": z.string().optional(),
    "email": z.string(),
    "displayName": z.string().optional(),
    "responseStatus": z.string(),
    "optional": z.boolean().optional(),
    "resource": z.boolean().optional(),
    "comment": z.string().optional(),
  }),
  "GmailDraftInput": z.object({
    "recipient": z.string(),
    "subject": z.string(),
    "body": z.string().optional(),
    "headers": z.record(z.string(), z.any()).optional(),
    "attachments": z.array(z.lazy(() => ACTION_INPUT_MODELS_ZOD["UrlAccessibleFile"] || z.any().describe("Lazy load failed: UrlAccessibleFile"))).optional(),
  }),
  "UrlAccessibleFile": z.object({
    "url": z.string(),
    "authentication": z.lazy(() => ACTION_INPUT_MODELS_ZOD["UrlAuthentication"] || z.any().describe("Lazy load failed: UrlAuthentication")),
  }),
  "UrlAuthentication": z.object({
    "providerKey": z.string(),
    "connectionId": z.string(),
  }),
  "GmailReplyDraftInput": z.object({
    "sender": z.string(),
    "subject": z.string(),
    "body": z.string(),
    "threadId": z.string(),
    "messageId": z.string(),
    "inReplyTo": z.string(),
    "references": z.string(),
    "date": z.string(),
    "replyBody": z.string(),
  }),
  "GmailMessageIdInput": z.object({
    "messageId": z.string(),
  }),
  "GmailGetMessageInput": z.object({
    "id": z.string(),
    "format": z.string().optional(),
  }),
  "GmailListMessagesInput": z.object({
    "maxResults": z.number().optional(),
    "labelIds": z.array(z.string()).optional(),
    "q": z.string().optional(),
    "pageToken": z.string().optional(),
  }),
  "GmailModifyMessageLabelsInput": z.object({
    "messageId": z.string(),
    "addLabelIds": z.array(z.string()).optional(),
    "removeLabelIds": z.array(z.string()).optional(),
  }),
  "GmailSendEmailInput": z.object({
    "to": z.string(),
    "subject": z.string(),
    "body": z.string(),
    "from": z.string().optional(),
    "cc": z.string().optional(),
    "bcc": z.string().optional(),
    "attachments": z.array(z.lazy(() => ACTION_INPUT_MODELS_ZOD["GmailAttachment"] || z.any().describe("Lazy load failed: GmailAttachment"))).optional(),
  }),
  "GmailAttachment": z.object({
    "filename": z.string(),
    "content": z.string(),
    "mimeType": z.string(),
  }),
  "DropboxCopyInput": z.object({
    "from_path": z.string(),
    "to_path": z.string(),
    "allow_shared_folder": z.boolean().optional(),
    "autorename": z.boolean().optional(),
  }),
  "DropboxCreateFolderInput": z.object({
    "path": z.string(),
    "autorename": z.boolean().optional(),
  }),
  "DropboxDeleteInput": z.object({
    "path": z.string(),
  }),
  "Anonymous_dropbox_action_fetchfile_input": z.object({
    "input": z.string(),
  }),
  "DropboxGetFileInput": z.object({
    "path": z.string(),
  }),
  "DropboxListFilesInput": z.object({
    "path": z.string(),
    "recursive": z.boolean().optional(),
    "limit": z.number().optional(),
    "include_deleted": z.boolean().optional(),
  }),
  "DropboxMoveInput": z.object({
    "from_path": z.string(),
    "to_path": z.string(),
    "allow_shared_folder": z.boolean().optional(),
    "autorename": z.boolean().optional(),
    "allow_ownership_transfer": z.boolean().optional(),
  }),
  "DropboxSearchInput": z.object({
    "query": z.string(),
    "path": z.string().optional(),
    "max_results": z.number().optional(),
    "mode": z.string().optional(),
  }),
  "DropboxUploadFileInput": z.object({
    "path": z.string(),
    "content": z.string(),
    "encoding": z.string().optional(),
    "mode": z.string().optional(),
    "autorename": z.boolean().optional(),
    "mute": z.boolean().optional(),
  }),
  "NotionCreateDatabaseInput": z.object({
    "parentId": z.string(),
    "title": z.array(z.lazy(() => ACTION_INPUT_MODELS_ZOD["NotionRichText"] || z.any().describe("Lazy load failed: NotionRichText"))),
    "properties": z.record(z.string(), z.any()),
  }),
  "NotionRichText": z.object({
    "type": z.string().optional(),
    "text": z.lazy(() => ACTION_INPUT_MODELS_ZOD["NotionRichTextContent"] || z.any().describe("Lazy load failed: NotionRichTextContent")).optional(),
    "annotations": z.lazy(() => ACTION_INPUT_MODELS_ZOD["NotionRichTextAnnotations"] || z.any().describe("Lazy load failed: NotionRichTextAnnotations")).optional(),
    "plain_text": z.string().optional(),
    "href": z.string().optional(),
  }),
  "NotionRichTextContent": z.object({
    "content": z.string().optional(),
    "link": z.literal("Record<string, any>").optional(),
  }),
  "NotionRichTextAnnotations": z.object({
    "bold": z.boolean().optional(),
    "italic": z.boolean().optional(),
    "strikethrough": z.boolean().optional(),
    "underline": z.boolean().optional(),
    "code": z.boolean().optional(),
    "color": z.string().optional(),
  }),
  "NotionCreatePageInput": z.object({
    "parentId": z.string(),
    "parentType": z.string().optional(),
    "properties": z.record(z.string(), z.any()),
    "children": z.array(z.record(z.string(), z.any())).optional(),
  }),
  "NotionGetDatabaseInput": z.object({
    "databaseId": z.string(),
  }),
  "NotionGetPageInput": z.object({
    "pageId": z.string(),
  }),
  "NotionQueryDatabaseInput": z.object({
    "databaseId": z.string(),
    "filter": z.record(z.string(), z.any()).optional(),
    "sorts": z.array(z.record(z.string(), z.any())).optional(),
    "start_cursor": z.string().optional(),
    "page_size": z.number().optional(),
  }),
  "NotionSearchInput": z.object({
    "query": z.string().optional(),
    "sort": z.lazy(() => ACTION_INPUT_MODELS_ZOD["NotionSort"] || z.any().describe("Lazy load failed: NotionSort")).optional(),
    "filter": z.lazy(() => ACTION_INPUT_MODELS_ZOD["NotionFilter"] || z.any().describe("Lazy load failed: NotionFilter")).optional(),
    "start_cursor": z.string().optional(),
    "page_size": z.number().optional(),
  }),
  "NotionSort": z.object({
    "direction": z.string().optional(),
    "timestamp": z.string().optional(),
  }),
  "NotionFilter": z.object({
    "value": z.string().optional(),
    "property": z.string().optional(),
  }),
  "NotionUpdateDatabaseInput": z.object({
    "databaseId": z.string(),
    "title": z.array(z.lazy(() => ACTION_INPUT_MODELS_ZOD["NotionRichText"] || z.any().describe("Lazy load failed: NotionRichText"))).optional(),
    "description": z.array(z.lazy(() => ACTION_INPUT_MODELS_ZOD["NotionRichText"] || z.any().describe("Lazy load failed: NotionRichText"))).optional(),
    "properties": z.record(z.string(), z.any()).optional(),
    "archived": z.boolean().optional(),
  }),
  "NotionUpdatePageInput": z.object({
    "pageId": z.string(),
    "properties": z.record(z.string(), z.any()).optional(),
    "archived": z.boolean().optional(),
    "icon": z.literal("Record<string, any>").optional(),
    "cover": z.literal("Record<string, any>").optional(),
  }),
  "GoogleDocsCreateDocumentInput": z.object({
    "title": z.string().optional(),
  }),
  "GoogleDocsGetDocumentInput": z.object({
    "documentId": z.string(),
  }),
  "GoogleDocsUpdateDocumentInput": z.object({
    "documentId": z.string(),
    "requests": z.array(z.record(z.string(), z.any())),
    "writeControl": z.record(z.string(), z.any()).optional(),
  }),
  "LinearCreateIssueInput": z.object({
    "teamId": z.string(),
    "title": z.string(),
    "description": z.string().optional(),
    "stateId": z.string().optional(),
    "assigneeId": z.string().optional(),
    "priority": z.number().optional(),
    "projectId": z.string().optional(),
    "labelIds": z.array(z.string()).optional(),
  }),
  "LinearCreateProjectInput": z.object({
    "name": z.string(),
    "description": z.string().optional(),
    "icon": z.string().optional(),
    "color": z.string().optional(),
    "teamIds": z.array(z.string()),
  }),
  "LinearIssueInput": z.object({
    "issueId": z.string(),
  }),
  "LinearProjectInput": z.object({
    "projectId": z.string(),
  }),
  "LinearTeamInput": z.object({
    "teamId": z.string(),
  }),
  "LinearIssuesInput": z.object({
    "teamId": z.string().optional(),
    "projectId": z.string().optional(),
    "states": z.array(z.string()).optional(),
    "assigneeId": z.string().optional(),
    "priority": z.number().optional(),
    "sortBy": z.string().optional(),
    "sortOrder": z.string().optional(),
    "limit": z.number().optional(),
    "first": z.number().optional(),
    "after": z.string().optional(),
  }),
  "LinearProjectsInput": z.object({
    "first": z.number().optional(),
    "after": z.string().optional(),
  }),
  "LinearTeamsInput": z.object({
    "first": z.number().optional(),
    "after": z.string().optional(),
  }),
  "LinearUpdateIssueInput": z.object({
    "issueId": z.string(),
    "title": z.string().optional(),
    "description": z.string().optional(),
    "stateId": z.string().optional(),
    "assigneeId": z.string().optional(),
    "priority": z.number().optional(),
    "projectId": z.string().optional(),
    "labelIds": z.array(z.string()).optional(),
  }),
  "LinearUpdateProjectInput": z.object({
    "projectId": z.string(),
    "name": z.string().optional(),
    "description": z.string().optional(),
    "icon": z.string().optional(),
    "color": z.string().optional(),
    "state": z.string().optional(),
    "teamIds": z.array(z.string()).optional(),
  }),
  "GoogleSheetCreateInput": z.object({
    "title": z.string(),
    "sheets": z.array(z.lazy(() => ACTION_INPUT_MODELS_ZOD["GoogleSheetTab"] || z.any().describe("Lazy load failed: GoogleSheetTab"))).optional(),
  }),
  "GoogleSheetTab": z.object({
    "title": z.string(),
    "data": z.lazy(() => ACTION_INPUT_MODELS_ZOD["SheetData"] || z.any().describe("Lazy load failed: SheetData")).optional(),
  }),
  "SheetData": z.object({
    "rows": z.array(z.lazy(() => ACTION_INPUT_MODELS_ZOD["SheetRow"] || z.any().describe("Lazy load failed: SheetRow"))),
  }),
  "SheetRow": z.object({
    "cells": z.array(z.string()),
  }),
  "SpreadsheetId": z.object({
    "id": z.string(),
  }),
  "GoogleSheetUpdateInput": z.object({
    "spreadsheetId": z.string(),
    "updates": z.array(z.lazy(() => ACTION_INPUT_MODELS_ZOD["SheetUpdate"] || z.any().describe("Lazy load failed: SheetUpdate"))),
  }),
  "SheetUpdate": z.object({
    "sheetId": z.number().optional(),
    "sheetName": z.string().optional(),
    "range": z.string().optional(),
    "startRow": z.number().optional(),
    "startColumn": z.number().optional(),
    "data": z.lazy(() => ACTION_INPUT_MODELS_ZOD["SheetData"] || z.any().describe("Lazy load failed: SheetData")),
  }),
  "IdEntity": z.object({
    "id": z.string(),
  }),
  "FolderContentInput": z.object({
    "id": z.string().optional(),
    "cursor": z.string().optional(),
  }),
  "ListDocumentsInput": z.object({
    "folderId": z.string().optional(),
    "mimeType": z.string().optional(),
    "pageSize": z.number().optional(),
    "pageToken": z.string().optional(),
    "orderBy": z.string().optional(),
  }),
  "UploadFileInput": z.object({
    "content": z.string(),
    "name": z.string(),
    "mimeType": z.string(),
    "folderId": z.union([z.string(), z.literal("undefined")]).optional(),
    "description": z.union([z.string(), z.literal("undefined")]).optional(),
    "isBase64": z.union([z.boolean(), z.literal("undefined")]).optional(),
  }),
  "LinkedInPostInput": z.object({
    "text": z.string(),
    "visibility": z.string(),
  }),
  "XSocialPostInput": z.object({
    "text": z.string(),
    "reply_to": z.string().optional(),
    "quote": z.string().optional(),
  }),
};

export const ACTION_OUTPUT_MODELS_ZOD: Record<string, z.ZodObject<any>> = {
  "HarvestTimeEntry": z.object({
    "id": z.number(),
    "spent_date": z.string(),
    "hours": z.number(),
    "notes": z.string().optional(),
    "is_locked": z.boolean(),
    "is_running": z.boolean(),
    "is_billed": z.boolean(),
    "timer_started_at": z.string().optional(),
    "started_time": z.string().optional(),
    "ended_time": z.string().optional(),
    "user": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["HarvestUser"] || z.any().describe("Lazy load failed: HarvestUser")),
    "client": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["HarvestClientInTimeEntry"] || z.any().describe("Lazy load failed: HarvestClientInTimeEntry")),
    "project": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["ProjectReference"] || z.any().describe("Lazy load failed: ProjectReference")),
    "task": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["HarvestTaskInTimeEntry"] || z.any().describe("Lazy load failed: HarvestTaskInTimeEntry")),
    "created_at": z.string(),
    "updated_at": z.string(),
    "hours_without_timer": z.number().optional(),
    "rounded_hours": z.number().optional(),
    "locked_reason": z.string().optional(),
    "is_closed": z.boolean().optional(),
    "billable": z.boolean().optional(),
    "budgeted": z.boolean().optional(),
    "billable_rate": z.number().optional(),
    "cost_rate": z.number().optional(),
    "user_assignment": z.record(z.string(), z.any()).optional(),
    "task_assignment": z.record(z.string(), z.any()).optional(),
    "invoice": z.literal("Record<string, any>").optional(),
    "external_reference": z.literal("HarvestExternalReferenceInput").optional(),
  }),
  "HarvestUser": z.object({
    "id": z.number(),
    "name": z.string(),
    "email": z.string().optional(),
  }),
  "HarvestClientInTimeEntry": z.object({
    "id": z.number(),
    "name": z.string(),
    "currency": z.string().optional(),
    "is_active": z.boolean().optional(),
  }),
  "ProjectReference": z.object({
    "id": z.number(),
    "name": z.string(),
    "code": z.string().optional(),
    "is_active": z.boolean().optional(),
    "is_billable": z.boolean().optional(),
  }),
  "HarvestTaskInTimeEntry": z.object({
    "id": z.number(),
    "name": z.string(),
    "is_active": z.boolean().optional(),
    "billable_by_default": z.boolean().optional(),
    "is_default": z.boolean().optional(),
    "created_at": z.string().optional(),
    "updated_at": z.string().optional(),
    "default_hourly_rate": z.number().optional(),
  }),
  "HarvestClient": z.object({
    "id": z.number(),
    "name": z.string(),
    "is_active": z.boolean(),
    "address": z.string().optional(),
    "statement_key": z.string().optional(),
    "created_at": z.string().optional(),
    "updated_at": z.string().optional(),
    "currency": z.string().optional(),
  }),
  "HarvestProject": z.object({
    "id": z.number(),
    "name": z.string(),
    "code": z.string().optional(),
    "client": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["HarvestClientReference"] || z.any().describe("Lazy load failed: HarvestClientReference")),
    "is_active": z.boolean(),
    "is_billable": z.boolean(),
    "is_fixed_fee": z.boolean(),
    "bill_by": z.string(),
    "budget": z.number().optional(),
    "budget_by": z.string(),
    "budget_is_monthly": z.boolean(),
    "notify_when_over_budget": z.boolean(),
    "over_budget_notification_percentage": z.number().optional(),
    "show_budget_to_all": z.boolean(),
    "created_at": z.string(),
    "updated_at": z.string(),
    "starts_on": z.string().optional(),
    "ends_on": z.string().optional(),
    "over_budget_notification_date": z.string().optional(),
    "notes": z.string().optional(),
    "cost_budget": z.number().optional(),
    "cost_budget_include_expenses": z.boolean(),
    "hourly_rate": z.number().optional(),
    "fee": z.number().optional(),
  }),
  "HarvestClientReference": z.object({
    "id": z.number(),
    "name": z.string(),
    "currency": z.string(),
  }),
  "HarvestDeleteProjectOutput": z.object({
    "success": z.boolean(),
    "message": z.string(),
  }),
  "HarvestDeleteTimeEntryOutput": z.object({
    "success": z.boolean(),
    "message": z.string(),
  }),
  "HarvestClientList": z.object({
    "clients": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["HarvestClient"] || z.any().describe("Lazy load failed: HarvestClient"))),
    "per_page": z.number(),
    "total_pages": z.number(),
    "total_entries": z.number(),
    "next_page": z.number().optional(),
    "previous_page": z.number().optional(),
    "page": z.number().optional(),
    "links": z.record(z.string(), z.any()).optional(),
  }),
  "HarvestProjectList": z.object({
    "projects": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["HarvestProject"] || z.any().describe("Lazy load failed: HarvestProject"))),
    "per_page": z.number(),
    "total_pages": z.number(),
    "total_entries": z.number(),
    "next_page": z.number().optional(),
    "previous_page": z.number().optional(),
    "page": z.number().optional(),
    "links": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["HarvestPaginationLinks"] || z.any().describe("Lazy load failed: HarvestPaginationLinks")).optional(),
  }),
  "HarvestPaginationLinks": z.object({
    "first": z.string().optional(),
    "next": z.string().optional(),
    "previous": z.string().optional(),
    "last": z.string().optional(),
  }),
  "HarvestProjectTaskList": z.object({
    "task_assignments": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["HarvestProjectTask"] || z.any().describe("Lazy load failed: HarvestProjectTask"))),
    "per_page": z.number().optional(),
    "total_pages": z.number().optional(),
    "total_entries": z.number().optional(),
    "next_page": z.number().optional(),
    "previous_page": z.number().optional(),
    "page": z.number().optional(),
    "links": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["PaginationLinks"] || z.any().describe("Lazy load failed: PaginationLinks")).optional(),
  }),
  "HarvestProjectTask": z.object({
    "id": z.number(),
    "billable": z.boolean(),
    "is_active": z.boolean(),
    "created_at": z.string(),
    "updated_at": z.string(),
    "hourly_rate": z.number().optional(),
    "budget": z.number().optional(),
    "project": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["ProjectReference"] || z.any().describe("Lazy load failed: ProjectReference")).optional(),
    "task": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["TaskInAssignment"] || z.any().describe("Lazy load failed: TaskInAssignment")),
  }),
  "TaskInAssignment": z.object({
    "id": z.number(),
    "name": z.string(),
  }),
  "PaginationLinks": z.object({
    "first": z.string().optional(),
    "next": z.string().optional(),
    "previous": z.string().optional(),
    "last": z.string().optional(),
  }),
  "HarvestTaskList": z.object({
    "tasks": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["HarvestTask"] || z.any().describe("Lazy load failed: HarvestTask"))),
    "per_page": z.number(),
    "total_pages": z.number(),
    "total_entries": z.number(),
    "next_page": z.number().optional(),
    "previous_page": z.number().optional(),
    "page": z.number(),
    "links": z.record(z.string(), z.any()).optional(),
  }),
  "HarvestTask": z.object({
    "id": z.number(),
    "name": z.string(),
    "is_active": z.boolean(),
    "billable_by_default": z.boolean(),
    "is_default": z.boolean().optional(),
    "created_at": z.string(),
    "updated_at": z.string(),
    "default_hourly_rate": z.number().optional(),
  }),
  "HarvestTimeEntryList": z.object({
    "time_entries": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["HarvestTimeEntry"] || z.any().describe("Lazy load failed: HarvestTimeEntry"))),
    "per_page": z.number(),
    "total_pages": z.number(),
    "total_entries": z.number(),
    "next_page": z.number().optional(),
    "previous_page": z.number().optional(),
    "page": z.number().optional(),
    "links": z.record(z.string(), z.any()).optional(),
  }),
  "GithubPullRequestComment": z.object({
    "id": z.number(),
    "node_id": z.string(),
    "url": z.string(),
    "pull_request_review_id": z.number(),
    "diff_hunk": z.string(),
    "path": z.string(),
    "position": z.number(),
    "original_position": z.number(),
    "commit_id": z.string(),
    "original_commit_id": z.string(),
    "user": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubIssueCreator"] || z.any().describe("Lazy load failed: GithubIssueCreator")),
    "body": z.string(),
    "created_at": z.string(),
    "updated_at": z.string(),
    "html_url": z.string(),
    "pull_request_url": z.string(),
    "author_association": z.string(),
    "_links": z.record(z.string(), z.any()),
    "reactions": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubReactions"] || z.any().describe("Lazy load failed: GithubReactions")),
    "start_line": z.number(),
    "original_start_line": z.number(),
    "start_side": z.string(),
    "line": z.number(),
    "original_line": z.number(),
    "side": z.string(),
    "in_reply_to_id": z.number().optional(),
    "subject_type": z.string(),
  }),
  "GithubIssueCreator": z.object({
    "login": z.string(),
    "id": z.number(),
    "node_id": z.string(),
    "avatar_url": z.string(),
    "gravatar_id": z.string(),
    "url": z.string(),
    "html_url": z.string(),
    "followers_url": z.string(),
    "following_url": z.string(),
    "gists_url": z.string(),
    "starred_url": z.string(),
    "subscriptions_url": z.string(),
    "organizations_url": z.string(),
    "repos_url": z.string(),
    "events_url": z.string(),
    "received_events_url": z.string(),
    "type": z.string(),
    "user_view_type": z.string(),
    "site_admin": z.boolean(),
  }),
  "GithubReactions": z.object({
    "url": z.string(),
    "total_count": z.number(),
    "+1": z.number(),
    "-1": z.number(),
    "laugh": z.number(),
    "hooray": z.number(),
    "confused": z.number(),
    "heart": z.number(),
    "rocket": z.number(),
    "eyes": z.number(),
  }),
  "GithubIssue": z.object({
    "id": z.number(),
    "node_id": z.string(),
    "url": z.string(),
    "repository_url": z.string(),
    "labels_url": z.string(),
    "comments_url": z.string(),
    "events_url": z.string(),
    "html_url": z.string(),
    "number": z.number(),
    "title": z.string(),
    "state": z.string(),
    "locked": z.boolean(),
    "body": z.string(),
    "user": z.union([z.literal("GithubIssueCreatorLite"), z.literal("GithubIssueCreator")]),
    "labels": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubIssueLabel"] || z.any().describe("Lazy load failed: GithubIssueLabel"))),
    "assignee": z.literal("Record<string, any>"),
    "assignees": z.array(z.record(z.string(), z.any())),
    "milestone": z.string(),
    "comments": z.number(),
    "created_at": z.string(),
    "updated_at": z.string(),
    "closed_at": z.string(),
    "author_association": z.string(),
    "active_lock_reason": z.string(),
    "sub_issues_summary": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubSubIssuesSummary"] || z.any().describe("Lazy load failed: GithubSubIssuesSummary")),
    "closed_by": z.literal("GithubIssueCreator"),
    "reactions": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubReactions"] || z.any().describe("Lazy load failed: GithubReactions")),
    "timeline_url": z.string(),
    "performed_via_github_app": z.literal("Record<string, any>"),
    "state_reason": z.string(),
  }),
  "GithubIssueLabel": z.object({
    "id": z.number(),
    "name": z.string(),
    "color": z.string(),
    "description": z.string(),
  }),
  "GithubSubIssuesSummary": z.object({
    "total": z.number(),
    "completed": z.number(),
    "percent_completed": z.number(),
  }),
  "GithubRepository": z.object({
    "id": z.number(),
    "node_id": z.string(),
    "name": z.string(),
    "full_name": z.string(),
    "private": z.boolean(),
    "owner": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubIssueCreator"] || z.any().describe("Lazy load failed: GithubIssueCreator")),
    "html_url": z.string(),
    "description": z.string(),
    "fork": z.boolean(),
    "url": z.string(),
    "forks_url": z.string(),
    "keys_url": z.string(),
    "collaborators_url": z.string(),
    "teams_url": z.string(),
    "hooks_url": z.string(),
    "issue_events_url": z.string(),
    "events_url": z.string(),
    "assignees_url": z.string(),
    "branches_url": z.string(),
    "tags_url": z.string(),
    "blobs_url": z.string(),
    "git_tags_url": z.string(),
    "git_refs_url": z.string(),
    "trees_url": z.string(),
    "statuses_url": z.string(),
    "languages_url": z.string(),
    "stargazers_url": z.string(),
    "contributors_url": z.string(),
    "subscribers_url": z.string(),
    "subscription_url": z.string(),
    "commits_url": z.string(),
    "git_commits_url": z.string(),
    "comments_url": z.string(),
    "issue_comment_url": z.string(),
    "contents_url": z.string(),
    "compare_url": z.string(),
    "merges_url": z.string(),
    "archive_url": z.string(),
    "downloads_url": z.string(),
    "issues_url": z.string(),
    "pulls_url": z.string(),
    "milestones_url": z.string(),
    "notifications_url": z.string(),
    "labels_url": z.string(),
    "releases_url": z.string(),
    "deployments_url": z.string(),
    "created_at": z.string(),
    "updated_at": z.string(),
    "pushed_at": z.string(),
    "git_url": z.string(),
    "ssh_url": z.string(),
    "clone_url": z.string(),
    "svn_url": z.string(),
    "homepage": z.string(),
    "size": z.number(),
    "stargazers_count": z.number(),
    "watchers_count": z.number(),
    "language": z.string(),
    "has_issues": z.boolean(),
    "has_projects": z.boolean(),
    "has_downloads": z.boolean(),
    "has_wiki": z.boolean(),
    "has_pages": z.boolean(),
    "has_discussions": z.boolean(),
    "forks_count": z.number(),
    "mirror_url": z.string(),
    "archived": z.boolean(),
    "disabled": z.boolean(),
    "open_issues_count": z.number(),
    "license": z.string(),
    "allow_forking": z.boolean(),
    "is_template": z.boolean(),
    "web_commit_signoff_required": z.boolean(),
    "topics": z.array(z.string()),
    "visibility": z.string(),
    "forks": z.number(),
    "open_issues": z.number(),
    "watchers": z.number(),
    "default_branch": z.string(),
  }),
  "GithubPullRequest": z.object({
    "url": z.string(),
    "id": z.number(),
    "node_id": z.string(),
    "html_url": z.string(),
    "diff_url": z.string(),
    "patch_url": z.string(),
    "issue_url": z.string(),
    "number": z.number(),
    "state": z.string(),
    "locked": z.boolean(),
    "title": z.string(),
    "user": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubIssueCreator"] || z.any().describe("Lazy load failed: GithubIssueCreator")),
    "body": z.string(),
    "created_at": z.string(),
    "updated_at": z.string(),
    "closed_at": z.string(),
    "merged_at": z.string(),
    "merge_commit_sha": z.string(),
    "assignee": z.literal("GithubIssueAssignee"),
    "assignees": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubIssueAssignee"] || z.any().describe("Lazy load failed: GithubIssueAssignee"))),
    "requested_reviewers": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubIssueAssignee"] || z.any().describe("Lazy load failed: GithubIssueAssignee"))),
    "requested_teams": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubTeamRef"] || z.any().describe("Lazy load failed: GithubTeamRef"))),
    "labels": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubIssueLabel"] || z.any().describe("Lazy load failed: GithubIssueLabel"))),
    "milestone": z.string(),
    "draft": z.boolean(),
    "commits_url": z.string(),
    "review_comments_url": z.string(),
    "review_comment_url": z.string(),
    "comments_url": z.string(),
    "statuses_url": z.string(),
    "head": z.record(z.string(), z.any()),
    "base": z.record(z.string(), z.any()),
    "_links": z.record(z.string(), z.any()),
    "author_association": z.string(),
    "auto_merge": z.literal("Record<string, any>"),
    "active_lock_reason": z.string(),
    "merged": z.boolean(),
    "mergeable": z.boolean(),
    "rebaseable": z.boolean(),
    "mergeable_state": z.string(),
    "merged_by": z.literal("GithubIssueCreator"),
    "comments": z.number(),
    "review_comments": z.number(),
    "maintainer_can_modify": z.boolean(),
    "commits": z.number(),
    "additions": z.number(),
    "deletions": z.number(),
    "changed_files": z.number(),
  }),
  "GithubIssueAssignee": z.object({
    "id": z.number(),
    "login": z.string(),
    "avatar_url": z.string(),
    "html_url": z.string(),
  }),
  "GithubTeamRef": z.object({
    "id": z.number(),
    "name": z.string(),
  }),
  "GithubPullRequestReview": z.object({
    "id": z.number(),
    "node_id": z.string(),
    "user": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubIssueCreator"] || z.any().describe("Lazy load failed: GithubIssueCreator")),
    "body": z.string(),
    "state": z.string(),
    "html_url": z.string(),
    "pull_request_url": z.string(),
    "submitted_at": z.string(),
    "commit_id": z.string(),
    "author_association": z.string(),
    "_links": z.record(z.string(), z.any()),
  }),
  "GithubDeleteRepositoryOutput": z.object({
    "success": z.boolean(),
    "message": z.string(),
  }),
  "GithubPullRequestCommentList": z.object({
    "comments": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubPullRequestComment"] || z.any().describe("Lazy load failed: GithubPullRequestComment"))),
  }),
  "GithubPullRequestFileList": z.object({
    "files": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubPullRequestFile"] || z.any().describe("Lazy load failed: GithubPullRequestFile"))),
  }),
  "GithubPullRequestFile": z.object({
    "sha": z.string(),
    "filename": z.string(),
    "status": z.string(),
    "additions": z.number(),
    "deletions": z.number(),
    "changes": z.number(),
    "blob_url": z.string(),
    "raw_url": z.string(),
    "contents_url": z.string(),
    "patch": z.string(),
  }),
  "GithubCombinedStatus": z.object({
    "state": z.string(),
    "sha": z.string(),
    "total_count": z.number(),
    "statuses": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubStatus"] || z.any().describe("Lazy load failed: GithubStatus"))),
    "repository": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubRepositoryForGithubCombinedStatus"] || z.any().describe("Lazy load failed: GithubRepositoryForGithubCombinedStatus")),
    "commit_url": z.string(),
  }),
  "GithubStatus": z.object({
    "url": z.string(),
    "id": z.number(),
    "node_id": z.string(),
    "state": z.string(),
    "context": z.string(),
    "description": z.string(),
    "target_url": z.string(),
    "created_at": z.string(),
    "updated_at": z.string(),
  }),
  "GithubRepositoryForGithubCombinedStatus": z.object({
    "id": z.number(),
    "node_id": z.string(),
    "name": z.string(),
    "full_name": z.string(),
    "private": z.boolean(),
    "owner": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubIssueCreator"] || z.any().describe("Lazy load failed: GithubIssueCreator")),
    "html_url": z.string(),
    "description": z.string(),
    "fork": z.boolean(),
    "url": z.string(),
    "forks_url": z.string(),
    "keys_url": z.string(),
    "collaborators_url": z.string(),
    "teams_url": z.string(),
    "hooks_url": z.string(),
    "issue_events_url": z.string(),
    "events_url": z.string(),
    "assignees_url": z.string(),
    "branches_url": z.string(),
    "tags_url": z.string(),
    "blobs_url": z.string(),
    "git_tags_url": z.string(),
    "git_refs_url": z.string(),
    "trees_url": z.string(),
    "statuses_url": z.string(),
    "languages_url": z.string(),
    "stargazers_url": z.string(),
    "contributors_url": z.string(),
    "subscribers_url": z.string(),
    "subscription_url": z.string(),
    "commits_url": z.string(),
    "git_commits_url": z.string(),
    "comments_url": z.string(),
    "issue_comment_url": z.string(),
    "contents_url": z.string(),
    "compare_url": z.string(),
    "merges_url": z.string(),
    "archive_url": z.string(),
    "downloads_url": z.string(),
    "issues_url": z.string(),
    "pulls_url": z.string(),
    "milestones_url": z.string(),
    "notifications_url": z.string(),
    "labels_url": z.string(),
    "releases_url": z.string(),
    "deployments_url": z.string(),
  }),
  "GithubBranchList": z.object({
    "branches": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubBranch"] || z.any().describe("Lazy load failed: GithubBranch"))),
  }),
  "GithubBranch": z.object({
    "name": z.string(),
    "commit": z.record(z.string(), z.any()),
    "protected": z.boolean(),
    "protection": z.record(z.string(), z.any()).optional(),
    "protection_url": z.string().optional(),
  }),
  "GithubIssueList": z.object({
    "issues": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubIssue"] || z.any().describe("Lazy load failed: GithubIssue"))),
  }),
  "GithubPullRequestList": z.object({
    "pull_requests": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubPullRequest"] || z.any().describe("Lazy load failed: GithubPullRequest"))),
  }),
  "GithubRepositoryList": z.object({
    "note": z.string(),
    "repositories": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GithubRepository"] || z.any().describe("Lazy load failed: GithubRepository"))),
  }),
  "GithubMergeResult": z.object({
    "sha": z.string(),
    "merged": z.boolean(),
    "message": z.string(),
  }),
  "GithubBranchUpdateResult": z.object({
    "message": z.string(),
    "url": z.string(),
  }),
  "GithubWriteFileActionResult": z.object({
    "url": z.string(),
    "status": z.string(),
    "sha": z.string(),
  }),
  "SlackReactionOutput": z.object({
    "ok": z.boolean(),
    "error": z.string().optional(),
  }),
  "SlackMessageList": z.object({
    "ok": z.boolean(),
    "messages": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["SlackMessage"] || z.any().describe("Lazy load failed: SlackMessage"))),
    "has_more": z.boolean().optional(),
    "pin_count": z.number().optional(),
    "channel_actions_ts": z.string().optional(),
    "channel_actions_count": z.number().optional(),
    "response_metadata": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["SlackResponseMetadata"] || z.any().describe("Lazy load failed: SlackResponseMetadata")).optional(),
    "error": z.string().optional(),
  }),
  "SlackMessage": z.object({
    "type": z.string(),
    "subtype": z.string().optional(),
    "ts": z.string(),
    "user": z.string().optional(),
    "text": z.string(),
    "thread_ts": z.string().optional(),
    "reply_count": z.number().optional(),
    "blocks": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["SlackBlock"] || z.any().describe("Lazy load failed: SlackBlock"))).optional(),
    "attachments": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["SlackAttachment"] || z.any().describe("Lazy load failed: SlackAttachment"))).optional(),
    "files": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["SlackFile"] || z.any().describe("Lazy load failed: SlackFile"))).optional(),
    "reactions": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["SlackReaction"] || z.any().describe("Lazy load failed: SlackReaction"))).optional(),
    "parent_user_id": z.string().optional(),
    "edited": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["SlackEdited"] || z.any().describe("Lazy load failed: SlackEdited")).optional(),
    "bot_id": z.string().optional(),
    "icons": z.record(z.string(), z.any()).optional(),
    "team": z.string().optional(),
    "app_id": z.string().optional(),
    "client_msg_id": z.string().optional(),
  }),
  "SlackBlock": z.object({
    "type": z.string(),
    "block_id": z.string().optional(),
    "text": z.record(z.string(), z.any()).optional(),
    "elements": z.array(z.record(z.string(), z.any())).optional(),
    "fields": z.array(z.record(z.string(), z.any())).optional(),
    "accessory": z.record(z.string(), z.any()).optional(),
  }),
  "SlackAttachment": z.object({
    "id": z.number().optional(),
    "fallback": z.string().optional(),
    "color": z.string().optional(),
    "pretext": z.string().optional(),
    "author_name": z.string().optional(),
    "author_link": z.string().optional(),
    "author_icon": z.string().optional(),
    "title": z.string().optional(),
    "title_link": z.string().optional(),
    "text": z.string().optional(),
    "fields": z.array(z.record(z.string(), z.any())).optional(),
    "image_url": z.string().optional(),
    "thumb_url": z.string().optional(),
    "footer": z.string().optional(),
    "footer_icon": z.string().optional(),
    "ts": z.number().optional(),
  }),
  "SlackFile": z.object({
    "id": z.string(),
    "name": z.string().optional(),
    "filetype": z.string().optional(),
    "url_private": z.string().optional(),
    "url_private_download": z.string().optional(),
    "mimetype": z.string().optional(),
    "size": z.number().optional(),
    "title": z.string().optional(),
    "created": z.number().optional(),
    "timestamp": z.number().optional(),
    "user": z.string().optional(),
    "editable": z.boolean().optional(),
    "mode": z.string().optional(),
    "is_external": z.boolean().optional(),
    "external_type": z.string().optional(),
    "permalink": z.string().optional(),
    "preview": z.string().optional(),
    "accessible": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["UrlAccessibleFile"] || z.any().describe("Lazy load failed: UrlAccessibleFile")).optional(),
  }),
  "UrlAccessibleFile": z.object({
    "url": z.string(),
    "authentication": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["UrlAuthentication"] || z.any().describe("Lazy load failed: UrlAuthentication")),
  }),
  "UrlAuthentication": z.object({
    "providerKey": z.string(),
    "connectionId": z.string(),
  }),
  "SlackReaction": z.object({
    "name": z.string(),
    "count": z.number(),
    "users": z.array(z.string()),
  }),
  "SlackEdited": z.object({
    "user": z.string(),
    "ts": z.string(),
  }),
  "SlackResponseMetadata": z.object({
    "next_cursor": z.string().optional(),
  }),
  "SlackPermalinkOutput": z.object({
    "ok": z.boolean(),
    "permalink": z.string().optional(),
    "channel": z.string().optional(),
    "error": z.string().optional(),
  }),
  "SlackUserInfo": z.object({
    "id": z.string(),
    "name": z.string(),
    "is_bot": z.boolean(),
    "is_admin": z.boolean().optional(),
    "is_owner": z.boolean().optional(),
    "tz": z.string().optional(),
    "profile": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["SlackUserProfile"] || z.any().describe("Lazy load failed: SlackUserProfile")).optional(),
  }),
  "SlackUserProfile": z.object({
    "real_name": z.string().optional(),
    "display_name": z.string().optional(),
    "email": z.string().optional(),
    "image_original": z.string().optional(),
    "image_512": z.string().optional(),
  }),
  "SlackConversationsList": z.object({
    "ok": z.boolean(),
    "channels": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["SlackConversation"] || z.any().describe("Lazy load failed: SlackConversation"))),
    "response_metadata": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["SlackResponseMetadata"] || z.any().describe("Lazy load failed: SlackResponseMetadata")).optional(),
    "error": z.string().optional(),
  }),
  "SlackConversation": z.object({
    "id": z.string(),
    "name": z.string().optional(),
    "is_channel": z.boolean().optional(),
    "is_group": z.boolean().optional(),
    "is_im": z.boolean().optional(),
    "is_mpim": z.boolean().optional(),
    "is_private": z.boolean().optional(),
    "is_member": z.boolean().optional(),
    "user": z.string().optional(),
    "num_members": z.number().optional(),
  }),
  "SlackSearchResultList": z.object({
    "ok": z.boolean(),
    "query": z.string(),
    "messages": z.record(z.string(), z.any()),
    "error": z.string().optional(),
  }),
  "SlackSendMessageOutput": z.object({
    "ok": z.boolean(),
    "ts": z.string(),
    "channel": z.string(),
    "message_text": z.string().optional(),
  }),
  "SlackUpdateMessageOutput": z.object({
    "ok": z.boolean(),
    "channel": z.string().optional(),
    "ts": z.string().optional(),
    "text": z.string().optional(),
    "error": z.string().optional(),
  }),
  "GoogleCalendarEvent": z.object({
    "id": z.string(),
    "kind": z.string(),
    "etag": z.string(),
    "status": z.string(),
    "htmlLink": z.string(),
    "created": z.string(),
    "updated": z.string(),
    "summary": z.string().describe("Event title / name."),
    "description": z.string().optional().describe("Contains e.g. the agenda or specifics of the meeting. Can contain HTML."),
    "location": z.string().optional().describe("Free form text."),
    "creator": z.any(),
    "organizer": z.any(),
    "start": z.any().describe("Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter."),
    "end": z.any().describe("Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter."),
    "iCalUID": z.string(),
    "sequence": z.number().optional(),
    "eventType": z.string(),
    "attendees": z.array(z.any()).optional().describe("A list of attendee email addresses."),
    "recurrence": z.array(z.string()).optional(),
    "recurringEventId": z.string().optional(),
    "reminders": z.any(),
    "hangoutLink": z.string().optional(),
    "conferenceData": z.any().optional(),
    "anyoneCanAddSelf": z.boolean().optional(),
    "guestsCanInviteOthers": z.boolean().optional(),
    "guestsCanSeeOtherGuests": z.boolean().optional(),
    "guestsCanModify": z.boolean().optional(),
    "privateCopy": z.boolean().optional(),
    "transparency": z.string().optional(),
    "visibility": z.string().optional(),
    "colorId": z.string().optional(),
    "attachments": z.array(z.any()).optional(),
  }),
  "GoogleCalendarEventDeleteOutput": z.object({
    "event": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GoogleCalendarEvent"] || z.any().describe("Lazy load failed: GoogleCalendarEvent")),
    "deletedAt": z.string(),
  }),
  "GoogleCalendarList": z.object({
    "calendars": z.array(z.any()),
    "nextPageToken": z.string().optional(),
  }),
  "GoogleCalendarEventList": z.object({
    "items": z.array(z.any()),
    "nextPageToken": z.string().optional(),
    "timeZone": z.string().describe("Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar."),
  }),
  "GmailDraftOutput": z.object({
    "id": z.string(),
    "threadId": z.string(),
  }),
  "GmailReplyDraftOutput": z.object({
    "id": z.string(),
    "threadId": z.string(),
  }),
  "GmailDeleteMessageOutput": z.object({
    "success": z.boolean(),
  }),
  "GmailMessage": z.object({
    "id": z.string(),
    "threadId": z.string(),
    "labelIds": z.array(z.string()).optional(),
    "snippet": z.string().optional(),
    "payload": z.record(z.string(), z.any()).optional(),
    "sizeEstimate": z.number().optional(),
    "historyId": z.string().optional(),
    "internalDate": z.string().optional(),
    "headers": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GmailHeader"] || z.any().describe("Lazy load failed: GmailHeader"))).optional(),
    "body": z.string().optional(),
    "mimeType": z.string().optional(),
    "filename": z.string().optional(),
    "attachments": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GmailAttachmentInfo"] || z.any().describe("Lazy load failed: GmailAttachmentInfo"))).optional(),
  }),
  "GmailHeader": z.object({
    "name": z.string(),
    "value": z.string(),
  }),
  "GmailAttachmentInfo": z.object({
    "filename": z.string(),
    "mimeType": z.string(),
    "size": z.number(),
    "attachmentId": z.string().optional(),
  }),
  "GmailMessageList": z.object({
    "messages": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GmailBasicMessageDetails"] || z.any().describe("Lazy load failed: GmailBasicMessageDetails"))),
    "nextPageToken": z.string().optional(),
  }),
  "GmailBasicMessageDetails": z.object({
    "id": z.string(),
    "threadId": z.string(),
    "labelIds": z.array(z.string()).optional(),
    "snippet": z.string().optional(),
    "subject": z.string().optional(),
    "date": z.string().optional(),
  }),
  "GmailSendEmailOutput": z.object({
    "id": z.string(),
    "threadId": z.string(),
    "labelIds": z.array(z.string()).optional(),
  }),
  "DropboxEntry": z.object({
    ".tag": z.string(),
    "id": z.string(),
    "name": z.string(),
    "path_display": z.string(),
    "path_lower": z.string(),
    "client_modified": z.string().optional(),
    "server_modified": z.string().optional(),
    "rev": z.string().optional(),
    "size": z.number().optional(),
    "is_downloadable": z.boolean().optional(),
    "content_hash": z.string().optional(),
  }),
  "DropboxFolder": z.object({
    "id": z.string(),
    "name": z.string(),
    "path_display": z.string(),
    "path_lower": z.string(),
  }),
  "DropboxDeleteResult": z.object({
    "metadata": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["DropboxEntry"] || z.any().describe("Lazy load failed: DropboxEntry")),
  }),
  "Anonymous_dropbox_action_fetchfile_output": z.object({
    "output": z.string(),
  }),
  "DropboxFile": z.object({
    "id": z.string(),
    "name": z.string(),
    "path_display": z.string(),
    "path_lower": z.string(),
    "size": z.number(),
    "content_hash": z.string().optional(),
    "server_modified": z.string(),
    "content": z.string().optional(),
    "download_url": z.string().optional(),
  }),
  "DropboxFileList": z.object({
    "entries": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["DropboxEntry"] || z.any().describe("Lazy load failed: DropboxEntry"))),
    "cursor": z.string().optional(),
    "has_more": z.boolean(),
  }),
  "DropboxSearchResult": z.object({
    "matches": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["DropboxSearchMatch"] || z.any().describe("Lazy load failed: DropboxSearchMatch"))),
    "more": z.boolean(),
    "start": z.number(),
  }),
  "DropboxSearchMatch": z.object({
    "metadata": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["DropboxEntry"] || z.any().describe("Lazy load failed: DropboxEntry")),
    "match_type": z.string(),
  }),
  "NotionDatabase": z.object({
    "object": z.string(),
    "id": z.string(),
    "created_time": z.string().optional(),
    "last_edited_time": z.string().optional(),
    "created_by": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionUserReference"] || z.any().describe("Lazy load failed: NotionUserReference")).optional(),
    "last_edited_by": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionUserReference"] || z.any().describe("Lazy load failed: NotionUserReference")).optional(),
    "icon": z.literal("NotionIcon").optional(),
    "cover": z.literal("NotionCover").optional(),
    "parent": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionParentReference"] || z.any().describe("Lazy load failed: NotionParentReference")).optional(),
    "archived": z.boolean().optional(),
    "in_trash": z.boolean().optional(),
    "properties": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionProperties"] || z.any().describe("Lazy load failed: NotionProperties")),
    "url": z.string().optional(),
    "public_url": z.string().optional(),
    "title": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionRichText"] || z.any().describe("Lazy load failed: NotionRichText"))).optional(),
    "description": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionRichText"] || z.any().describe("Lazy load failed: NotionRichText"))).optional(),
    "is_inline": z.boolean().optional(),
    "request_id": z.string().optional(),
  }),
  "NotionUserReference": z.object({
    "object": z.string(),
    "id": z.string(),
  }),
  "NotionParentReference": z.object({
    "type": z.string(),
    "workspace": z.boolean().optional(),
    "page_id": z.string().optional(),
    "database_id": z.string().optional(),
  }),
  "NotionProperties": z.object({
    "title": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionTitleProperty"] || z.any().describe("Lazy load failed: NotionTitleProperty")).optional(),
  }),
  "NotionTitleProperty": z.object({
    "id": z.string().optional(),
    "type": z.string().optional(),
    "title": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionRichText"] || z.any().describe("Lazy load failed: NotionRichText"))).optional(),
  }),
  "NotionRichText": z.object({
    "type": z.string().optional(),
    "text": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionRichTextContent"] || z.any().describe("Lazy load failed: NotionRichTextContent")).optional(),
    "annotations": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionRichTextAnnotations"] || z.any().describe("Lazy load failed: NotionRichTextAnnotations")).optional(),
    "plain_text": z.string().optional(),
    "href": z.string().optional(),
  }),
  "NotionRichTextContent": z.object({
    "content": z.string().optional(),
    "link": z.literal("Record<string, any>").optional(),
  }),
  "NotionRichTextAnnotations": z.object({
    "bold": z.boolean().optional(),
    "italic": z.boolean().optional(),
    "strikethrough": z.boolean().optional(),
    "underline": z.boolean().optional(),
    "code": z.boolean().optional(),
    "color": z.string().optional(),
  }),
  "NotionPageOrDatabase": z.object({
    "object": z.string(),
    "id": z.string(),
    "created_time": z.string().optional(),
    "last_edited_time": z.string().optional(),
    "created_by": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionUserReference"] || z.any().describe("Lazy load failed: NotionUserReference")).optional(),
    "last_edited_by": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionUserReference"] || z.any().describe("Lazy load failed: NotionUserReference")).optional(),
    "cover": z.literal("NotionCover").optional(),
    "icon": z.literal("NotionIcon").optional(),
    "parent": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionParentReference"] || z.any().describe("Lazy load failed: NotionParentReference")).optional(),
    "archived": z.boolean().optional(),
    "in_trash": z.boolean().optional(),
    "properties": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionProperties"] || z.any().describe("Lazy load failed: NotionProperties")).optional(),
    "url": z.string().optional(),
    "public_url": z.string().optional(),
    "title": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionRichText"] || z.any().describe("Lazy load failed: NotionRichText"))).optional(),
    "description": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionRichText"] || z.any().describe("Lazy load failed: NotionRichText"))).optional(),
    "is_inline": z.boolean().optional(),
    "request_id": z.string().optional(),
  }),
  "NotionQueryDatabaseOutput": z.object({
    "object": z.string(),
    "results": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionPageOrDatabase"] || z.any().describe("Lazy load failed: NotionPageOrDatabase"))),
    "next_cursor": z.string().optional(),
    "has_more": z.boolean(),
    "type": z.string().optional(),
    "page": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionGenericObjectPlaceholder"] || z.any().describe("Lazy load failed: NotionGenericObjectPlaceholder")).optional(),
    "request_id": z.string().optional(),
  }),
  "NotionGenericObjectPlaceholder": z.object({
    "_placeholder": z.string().optional(),
  }),
  "NotionSearchOutput": z.object({
    "object": z.string(),
    "results": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionPageOrDatabase"] || z.any().describe("Lazy load failed: NotionPageOrDatabase"))),
    "next_cursor": z.string().optional(),
    "has_more": z.boolean(),
    "type": z.string().optional(),
    "page_or_database": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["NotionGenericObjectPlaceholder"] || z.any().describe("Lazy load failed: NotionGenericObjectPlaceholder")).optional(),
    "request_id": z.string().optional(),
  }),
  "GoogleDocsDocument": z.object({
    "documentId": z.string().optional(),
    "title": z.string().optional(),
    "body": z.record(z.string(), z.any()).optional(),
    "headers": z.record(z.string(), z.any()).optional(),
    "footers": z.record(z.string(), z.any()).optional(),
    "footnotes": z.record(z.string(), z.any()).optional(),
    "documentStyle": z.record(z.string(), z.any()).optional(),
    "namedStyles": z.record(z.string(), z.any()).optional(),
    "revisionId": z.string().optional(),
    "suggestionsViewMode": z.string().optional(),
    "inlineObjects": z.record(z.string(), z.any()).optional(),
    "positionedObjects": z.record(z.string(), z.any()).optional(),
    "tabs": z.array(z.record(z.string(), z.any())).optional(),
  }),
  "GoogleDocsUpdateDocumentOutput": z.object({
    "documentId": z.string(),
    "replies": z.array(z.record(z.string(), z.any())).optional(),
    "writeControl": z.record(z.string(), z.any()).optional(),
  }),
  "LinearIssue": z.object({
    "id": z.string(),
    "title": z.string(),
    "description": z.string().optional(),
    "number": z.number(),
    "priority": z.number(),
    "url": z.string(),
    "createdAt": z.string(),
    "updatedAt": z.string(),
    "state": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["LinearState"] || z.any().describe("Lazy load failed: LinearState")),
    "assignee": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["LinearUser"] || z.any().describe("Lazy load failed: LinearUser")).optional(),
    "team": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["LinearTeamBasic"] || z.any().describe("Lazy load failed: LinearTeamBasic")),
    "project": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["LinearProjectBasic"] || z.any().describe("Lazy load failed: LinearProjectBasic")).optional(),
    "labels": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["LinearLabel"] || z.any().describe("Lazy load failed: LinearLabel"))).optional(),
  }),
  "LinearState": z.object({
    "id": z.string(),
    "name": z.string(),
    "color": z.string(),
    "type": z.string(),
  }),
  "LinearUser": z.object({
    "id": z.string(),
    "name": z.string(),
    "email": z.string(),
    "displayName": z.string().optional(),
    "avatarUrl": z.string().optional(),
  }),
  "LinearTeamBasic": z.object({
    "id": z.string(),
    "name": z.string(),
    "key": z.string(),
  }),
  "LinearProjectBasic": z.object({
    "id": z.string(),
    "name": z.string(),
    "icon": z.string().optional(),
    "color": z.string().optional(),
  }),
  "LinearLabel": z.object({
    "id": z.string(),
    "name": z.string(),
    "color": z.string(),
  }),
  "LinearProject": z.object({
    "id": z.string(),
    "name": z.string(),
    "description": z.string().optional(),
    "url": z.string().optional(),
    "color": z.string().optional(),
    "state": z.string(),
    "lead": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["LinearUserBasic"] || z.any().describe("Lazy load failed: LinearUserBasic")).optional(),
    "teams": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["LinearTeamBasic"] || z.any().describe("Lazy load failed: LinearTeamBasic"))),
    "createdAt": z.string(),
    "updatedAt": z.string(),
  }),
  "LinearUserBasic": z.object({
    "id": z.string(),
    "name": z.string(),
    "email": z.string().optional(),
  }),
  "LinearDeleteIssueOutput": z.object({
    "success": z.boolean(),
    "issueId": z.string(),
  }),
  "ModelResponse": z.object({
    "models": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["Model"] || z.any().describe("Lazy load failed: Model"))),
  }),
  "Model": z.object({
    "name": z.string(),
  }),
  "LinearTeam": z.object({
    "id": z.string(),
    "name": z.string(),
    "key": z.string(),
    "description": z.string().optional(),
    "color": z.string().optional(),
    "private": z.boolean().optional(),
    "createdAt": z.string(),
    "updatedAt": z.string(),
    "members": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["LinearUser"] || z.any().describe("Lazy load failed: LinearUser"))),
  }),
  "LinearIssueList": z.object({
    "issues": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["LinearIssue"] || z.any().describe("Lazy load failed: LinearIssue"))),
    "pageInfo": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["PageInfo"] || z.any().describe("Lazy load failed: PageInfo")).optional(),
  }),
  "PageInfo": z.object({
    "hasNextPage": z.boolean(),
    "endCursor": z.string().optional(),
  }),
  "LinearProjectList": z.object({
    "projects": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["LinearProject"] || z.any().describe("Lazy load failed: LinearProject"))),
    "pageInfo": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["PageInfo"] || z.any().describe("Lazy load failed: PageInfo")).optional(),
  }),
  "LinearTeamList": z.object({
    "teams": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["LinearTeam"] || z.any().describe("Lazy load failed: LinearTeam"))),
    "pageInfo": z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["PageInfo"] || z.any().describe("Lazy load failed: PageInfo")).optional(),
  }),
  "GoogleSheetCreateOutput": z.object({
    "id": z.string(),
    "url": z.string(),
    "title": z.string(),
  }),
  "Spreadsheet": z.object({
    "spreadsheetId": z.string(),
    "properties": z.record(z.string(), z.any()),
    "sheets": z.array(z.record(z.string(), z.any())),
    "namedRanges": z.array(z.record(z.string(), z.any())),
    "spreadsheetUrl": z.string(),
    "developerMetadata": z.array(z.record(z.string(), z.any())),
    "dataSources": z.array(z.record(z.string(), z.any())),
    "dataSourceSchedules": z.array(z.record(z.string(), z.any())),
  }),
  "GoogleSheetUpdateOutput": z.object({
    "spreadsheetId": z.string(),
    "updatedRange": z.string(),
    "updatedRows": z.number(),
    "updatedColumns": z.number(),
    "updatedCells": z.number(),
  }),
  "Anonymous_googledrive_action_fetchdocument_output": z.object({
    "output": z.string(),
  }),
  "JSONDocument": z.object({
    "documentId": z.string(),
    "title": z.string(),
    "url": z.string(),
    "tabs": z.array(z.record(z.string(), z.any())),
    "revisionId": z.string(),
    "suggestionsViewMode": z.union([z.literal("DEFAULT_FOR_CURRENT_ACCESS"), z.literal("SUGGESTIONS_INLINE"), z.literal("PREVIEW_SUGGESTIONS_ACCEPTED"), z.literal("PREVIEW_WITHOUT_SUGGESTIONS")]),
    "body": z.record(z.string(), z.any()),
    "headers": z.record(z.string(), z.any()),
    "footers": z.record(z.string(), z.any()),
    "footnotes": z.record(z.string(), z.any()),
    "documentStyle": z.record(z.string(), z.any()),
    "suggestedDocumentStyleChanges": z.record(z.string(), z.any()),
    "namedStyles": z.record(z.string(), z.any()),
    "suggestedNamedStylesChanges": z.record(z.string(), z.any()),
    "lists": z.record(z.string(), z.any()),
    "namedRanges": z.record(z.string(), z.any()),
    "inlineObjects": z.record(z.string(), z.any()),
    "positionedObjects": z.record(z.string(), z.any()),
  }),
  "JSONSpreadsheet": z.object({
    "spreadsheetId": z.string(),
    "properties": z.record(z.string(), z.any()),
    "sheets": z.array(z.record(z.string(), z.any())),
    "namedRanges": z.array(z.record(z.string(), z.any())),
    "spreadsheetUrl": z.string(),
    "developerMetadata": z.array(z.record(z.string(), z.any())),
    "dataSources": z.array(z.record(z.string(), z.any())),
    "dataSourceSchedules": z.array(z.record(z.string(), z.any())),
  }),
  "FolderContent": z.object({
    "files": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GoogleDocument"] || z.any().describe("Lazy load failed: GoogleDocument"))),
    "folders": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GoogleDocument"] || z.any().describe("Lazy load failed: GoogleDocument"))),
    "cursor": z.string().optional(),
  }),
  "GoogleDocument": z.object({
    "id": z.string(),
    "name": z.string(),
    "mimeType": z.string(),
    "parents": z.array(z.string()).optional(),
    "modifiedTime": z.string().optional(),
    "createdTime": z.string().optional(),
    "webViewLink": z.string().optional(),
    "kind": z.string().optional(),
  }),
  "GoogleDriveDocumentList": z.object({
    "documents": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GoogleDriveDocument"] || z.any().describe("Lazy load failed: GoogleDriveDocument"))),
    "nextPageToken": z.string().optional(),
  }),
  "GoogleDriveDocument": z.object({
    "id": z.string(),
    "name": z.string(),
    "mimeType": z.string(),
    "webViewLink": z.string(),
    "modifiedTime": z.string(),
    "createdTime": z.string(),
    "parents": z.array(z.string()),
    "size": z.string(),
  }),
  "GoogleDriveFolderList": z.object({
    "folders": z.array(z.lazy(() => ACTION_OUTPUT_MODELS_ZOD["GoogleDriveFolder"] || z.any().describe("Lazy load failed: GoogleDriveFolder"))),
    "nextPageToken": z.string().optional(),
  }),
  "GoogleDriveFolder": z.object({
    "id": z.string(),
    "name": z.string(),
    "mimeType": z.string(),
    "webViewLink": z.string(),
    "modifiedTime": z.string(),
    "createdTime": z.string(),
    "parents": z.array(z.string()),
  }),
  "LinkedInUserProfile": z.object({
    "sub": z.string(),
    "email_verified": z.boolean(),
    "name": z.string(),
    "locale": z.record(z.string(), z.any()),
    "given_name": z.string(),
    "family_name": z.string(),
    "email": z.string(),
    "picture": z.string(),
  }),
  "LinkedInPostOutput": z.object({
    "id": z.string(),
  }),
  "XSocialUserProfile": z.object({
    "id": z.string(),
    "name": z.string(),
    "username": z.string(),
    "profile_image_url": z.string(),
    "description": z.string(),
    "location": z.string(),
    "url": z.string(),
    "protected": z.boolean(),
    "verified": z.boolean(),
    "followers_count": z.number(),
    "following_count": z.number(),
    "tweet_count": z.number(),
    "listed_count": z.number(),
  }),
  "XSocialPostOutput": z.object({
    "id": z.string(),
    "text": z.string(),
    "created_at": z.string(),
  }),
};

export const SYNC_OUTPUT_MODELS_ZOD: Record<string, z.ZodObject<any>> = {
  "SlackSyncMessage": z.object({
    "id": z.string(),
    "channel_id": z.string(),
    "message": z.lazy(() => SYNC_OUTPUT_MODELS_ZOD["SlackMessage"] || z.any().describe("Lazy load failed: SlackMessage")),
  }),
  "SlackMessage": z.object({
    "type": z.string(),
    "subtype": z.string().optional(),
    "ts": z.string(),
    "user": z.string().optional(),
    "text": z.string(),
    "thread_ts": z.string().optional(),
    "reply_count": z.number().optional(),
    "blocks": z.array(z.lazy(() => SYNC_OUTPUT_MODELS_ZOD["SlackBlock"] || z.any().describe("Lazy load failed: SlackBlock"))).optional(),
    "attachments": z.array(z.lazy(() => SYNC_OUTPUT_MODELS_ZOD["SlackAttachment"] || z.any().describe("Lazy load failed: SlackAttachment"))).optional(),
    "files": z.array(z.lazy(() => SYNC_OUTPUT_MODELS_ZOD["SlackFile"] || z.any().describe("Lazy load failed: SlackFile"))).optional(),
    "reactions": z.array(z.lazy(() => SYNC_OUTPUT_MODELS_ZOD["SlackReaction"] || z.any().describe("Lazy load failed: SlackReaction"))).optional(),
    "parent_user_id": z.string().optional(),
    "edited": z.lazy(() => SYNC_OUTPUT_MODELS_ZOD["SlackEdited"] || z.any().describe("Lazy load failed: SlackEdited")).optional(),
    "bot_id": z.string().optional(),
    "icons": z.record(z.string(), z.any()).optional(),
    "team": z.string().optional(),
    "app_id": z.string().optional(),
    "client_msg_id": z.string().optional(),
  }),
  "SlackBlock": z.object({
    "type": z.string(),
    "block_id": z.string().optional(),
    "text": z.record(z.string(), z.any()).optional(),
    "elements": z.array(z.record(z.string(), z.any())).optional(),
    "fields": z.array(z.record(z.string(), z.any())).optional(),
    "accessory": z.record(z.string(), z.any()).optional(),
  }),
  "SlackAttachment": z.object({
    "id": z.number().optional(),
    "fallback": z.string().optional(),
    "color": z.string().optional(),
    "pretext": z.string().optional(),
    "author_name": z.string().optional(),
    "author_link": z.string().optional(),
    "author_icon": z.string().optional(),
    "title": z.string().optional(),
    "title_link": z.string().optional(),
    "text": z.string().optional(),
    "fields": z.array(z.record(z.string(), z.any())).optional(),
    "image_url": z.string().optional(),
    "thumb_url": z.string().optional(),
    "footer": z.string().optional(),
    "footer_icon": z.string().optional(),
    "ts": z.number().optional(),
  }),
  "SlackFile": z.object({
    "id": z.string(),
    "name": z.string().optional(),
    "filetype": z.string().optional(),
    "url_private": z.string().optional(),
    "url_private_download": z.string().optional(),
    "mimetype": z.string().optional(),
    "size": z.number().optional(),
    "title": z.string().optional(),
    "created": z.number().optional(),
    "timestamp": z.number().optional(),
    "user": z.string().optional(),
    "editable": z.boolean().optional(),
    "mode": z.string().optional(),
    "is_external": z.boolean().optional(),
    "external_type": z.string().optional(),
    "permalink": z.string().optional(),
    "preview": z.string().optional(),
    "accessible": z.lazy(() => SYNC_OUTPUT_MODELS_ZOD["UrlAccessibleFile"] || z.any().describe("Lazy load failed: UrlAccessibleFile")).optional(),
  }),
  "UrlAccessibleFile": z.object({
    "url": z.string(),
    "authentication": z.lazy(() => SYNC_OUTPUT_MODELS_ZOD["UrlAuthentication"] || z.any().describe("Lazy load failed: UrlAuthentication")),
  }),
  "UrlAuthentication": z.object({
    "providerKey": z.string(),
    "connectionId": z.string(),
  }),
  "SlackReaction": z.object({
    "name": z.string(),
    "count": z.number(),
    "users": z.array(z.string()),
  }),
  "SlackEdited": z.object({
    "user": z.string(),
    "ts": z.string(),
  }),
  "GoogleCalendarEvent": z.object({
    "id": z.string(),
    "kind": z.string(),
    "etag": z.string(),
    "status": z.string(),
    "htmlLink": z.string(),
    "created": z.string(),
    "updated": z.string(),
    "summary": z.string(),
    "description": z.string().optional(),
    "location": z.string().optional(),
    "creator": z.any(),
    "organizer": z.any(),
    "start": z.any(),
    "end": z.any(),
    "iCalUID": z.string(),
    "sequence": z.number().optional(),
    "eventType": z.string(),
    "attendees": z.array(z.any()).optional(),
    "recurrence": z.array(z.string()).optional(),
    "recurringEventId": z.string().optional(),
    "reminders": z.any(),
    "hangoutLink": z.string().optional(),
    "conferenceData": z.any().optional(),
    "anyoneCanAddSelf": z.boolean().optional(),
    "guestsCanInviteOthers": z.boolean().optional(),
    "guestsCanSeeOtherGuests": z.boolean().optional(),
    "guestsCanModify": z.boolean().optional(),
    "privateCopy": z.boolean().optional(),
    "transparency": z.string().optional(),
    "visibility": z.string().optional(),
    "colorId": z.string().optional(),
    "attachments": z.array(z.any()).optional(),
  }),
  "GmailEmail": z.object({
    "id": z.string(),
    "sender": z.string(),
    "recipients": z.string(),
    "date": z.string(),
    "subject": z.string(),
    "body": z.string(),
    "attachments": z.array(z.lazy(() => SYNC_OUTPUT_MODELS_ZOD["Attachments"] || z.any().describe("Lazy load failed: Attachments"))),
    "threadId": z.string(),
    "isDraft": z.boolean(),
    "labels": z.array(z.string()),
    "snippet": z.string(),
    "cc": z.string(),
    "bcc": z.string(),
    "messageId": z.string(),
    "inReplyTo": z.string(),
    "references": z.string(),
  }),
  "Attachments": z.object({
    "filename": z.string(),
    "mimeType": z.string(),
    "size": z.number(),
    "attachmentId": z.string(),
  }),
  "DropboxFile": z.object({
    "id": z.string(),
    "name": z.string(),
    "path_display": z.string(),
    "path_lower": z.string(),
    "size": z.number(),
    "content_hash": z.string().optional(),
    "server_modified": z.string(),
    "content": z.string().optional(),
    "download_url": z.string().optional(),
  }),
  "Document": z.object({
    "id": z.string(),
    "url": z.string(),
    "mimeType": z.string(),
    "title": z.string(),
    "updatedAt": z.string(),
  }),
};

export const ACTION_INPUT_MODELS_JSON_SCHEMA = {
  "HarvestAddHistoricalTimeEntryInput": {
    "type": "object",
    "properties": {
      "project_id": {
        "type": "number"
      },
      "task_id": {
        "type": "number"
      },
      "spent_date": {
        "type": "string"
      },
      "hours": {
        "type": "number"
      },
      "started_time": {
        "type": "string"
      },
      "ended_time": {
        "type": "string"
      },
      "notes": {
        "type": "string"
      },
      "user_id": {
        "type": "number"
      },
      "external_reference": {
        "type": {
          "$ref": "HarvestExternalReferenceInput"
        }
      }
    },
    "required": [
      "project_id",
      "task_id",
      "spent_date"
    ]
  },
  "HarvestExternalReferenceInput": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "group_id": {
        "type": "string"
      },
      "account_id": {
        "type": "string"
      },
      "permalink": {
        "type": "string"
      },
      "service": {
        "type": "string"
      },
      "service_icon_url": {
        "type": "string"
      }
    },
    "required": []
  },
  "HarvestCreateClientInput": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string"
      },
      "is_active": {
        "type": "boolean"
      },
      "address": {
        "type": [
          "string",
          null
        ]
      },
      "currency": {
        "type": [
          "string",
          null
        ]
      }
    },
    "required": [
      "name"
    ]
  },
  "HarvestCreateProjectInput": {
    "type": "object",
    "properties": {
      "client_id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      },
      "is_billable": {
        "type": "boolean"
      },
      "bill_by": {
        "type": "string"
      },
      "budget_by": {
        "type": "string"
      },
      "is_fixed_fee": {
        "type": "boolean"
      },
      "fee": {
        "type": [
          "number",
          null
        ]
      },
      "hourly_rate": {
        "type": [
          "number",
          null
        ]
      },
      "budget": {
        "type": [
          "number",
          null
        ]
      },
      "budget_is_monthly": {
        "type": "boolean"
      },
      "notify_when_over_budget": {
        "type": "boolean"
      },
      "over_budget_notification_percentage": {
        "type": "number"
      },
      "show_budget_to_all": {
        "type": "boolean"
      },
      "cost_budget": {
        "type": [
          "number",
          null
        ]
      },
      "cost_budget_include_expenses": {
        "type": "boolean"
      },
      "notes": {
        "type": [
          "string",
          null
        ]
      },
      "starts_on": {
        "type": [
          "string",
          null
        ]
      },
      "ends_on": {
        "type": [
          "string",
          null
        ]
      }
    },
    "required": [
      "client_id",
      "name",
      "is_billable",
      "bill_by",
      "budget_by"
    ]
  },
  "HarvestProjectInput": {
    "type": "object",
    "properties": {
      "project_id": {
        "type": "number"
      }
    },
    "required": [
      "project_id"
    ]
  },
  "HarvestTimeEntryInput": {
    "type": "object",
    "properties": {
      "timeEntryId": {
        "type": "number"
      },
      "id": {
        "type": "number"
      }
    },
    "required": [
      "timeEntryId"
    ]
  },
  "HarvestClientInput": {
    "type": "object",
    "properties": {
      "client_id": {
        "type": "number"
      }
    },
    "required": [
      "client_id"
    ]
  },
  "HarvestProjectsInput": {
    "type": "object",
    "properties": {
      "client_id": {
        "type": "number"
      },
      "is_active": {
        "type": "boolean"
      },
      "page": {
        "type": "number"
      },
      "per_page": {
        "type": "number"
      }
    },
    "required": []
  },
  "HarvestProjectTasksInput": {
    "type": "object",
    "properties": {
      "project_id": {
        "type": "number"
      }
    },
    "required": [
      "project_id"
    ]
  },
  "HarvestTasksInput": {
    "type": "object",
    "properties": {
      "is_active": {
        "type": "boolean"
      },
      "updated_since": {
        "type": "string"
      },
      "page": {
        "type": "number"
      },
      "per_page": {
        "type": "number"
      }
    },
    "required": []
  },
  "HarvestTimeEntriesInput": {
    "type": "object",
    "properties": {
      "userId": {
        "type": "number"
      },
      "clientId": {
        "type": "number"
      },
      "projectId": {
        "type": "number"
      },
      "taskId": {
        "type": "number"
      },
      "from": {
        "type": "string"
      },
      "to": {
        "type": "string"
      },
      "page": {
        "type": "number"
      },
      "perPage": {
        "type": "number"
      }
    },
    "required": []
  },
  "HarvestStartTimerInput": {
    "type": "object",
    "properties": {
      "project_id": {
        "type": "number"
      },
      "task_id": {
        "type": "number"
      },
      "spent_date": {
        "type": "string"
      },
      "started_time": {
        "type": "string"
      },
      "notes": {
        "type": "string"
      },
      "user_id": {
        "type": "number"
      },
      "external_reference": {
        "type": {
          "$ref": "HarvestExternalReferenceInput"
        }
      }
    },
    "required": [
      "project_id",
      "task_id",
      "spent_date"
    ]
  },
  "HarvestUpdateTimeEntryInput": {
    "type": "object",
    "properties": {
      "time_entry_id": {
        "type": "number"
      },
      "project_id": {
        "type": "number"
      },
      "task_id": {
        "type": "number"
      },
      "spent_date": {
        "type": "string"
      },
      "hours": {
        "type": "number"
      },
      "started_time": {
        "type": "string"
      },
      "ended_time": {
        "type": "string"
      },
      "notes": {
        "type": "string"
      },
      "external_reference": {
        "type": {
          "$ref": "HarvestExternalReferenceInput"
        }
      }
    },
    "required": [
      "time_entry_id"
    ]
  },
  "GithubAddPullRequestReviewCommentInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "pull_number": {
        "type": "number"
      },
      "body": {
        "type": "string"
      },
      "commit_id": {
        "type": "string"
      },
      "path": {
        "type": "string"
      },
      "subject_type": {
        "type": "string"
      },
      "line": {
        "type": "number"
      },
      "side": {
        "type": "string"
      },
      "start_line": {
        "type": "number"
      },
      "start_side": {
        "type": "string"
      },
      "in_reply_to": {
        "type": "number"
      },
      "diff_hunk": {
        "type": "string"
      }
    },
    "required": [
      "owner",
      "repo",
      "pull_number",
      "body"
    ]
  },
  "GithubCreateIssueInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "title": {
        "type": "string"
      },
      "body": {
        "type": "string"
      },
      "assignees": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "labels": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "owner",
      "repo",
      "title"
    ]
  },
  "GithubCreateOrganizationRepositoryInput": {
    "type": "object",
    "properties": {
      "org": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "homepage": {
        "type": "string"
      },
      "private": {
        "type": "boolean"
      },
      "has_issues": {
        "type": "boolean"
      },
      "has_projects": {
        "type": "boolean"
      },
      "has_wiki": {
        "type": "boolean"
      }
    },
    "required": [
      "org",
      "name"
    ]
  },
  "GithubCreatePullRequestInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "title": {
        "type": "string"
      },
      "body": {
        "type": "string"
      },
      "head": {
        "type": "string"
      },
      "base": {
        "type": "string"
      },
      "draft": {
        "type": "boolean"
      },
      "maintainer_can_modify": {
        "type": "boolean"
      }
    },
    "required": [
      "owner",
      "repo",
      "title",
      "head",
      "base"
    ]
  },
  "GithubCreatePullRequestReviewInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "pullNumber": {
        "type": "number"
      },
      "body": {
        "type": "string"
      },
      "event": {
        "type": "string"
      },
      "commitId": {
        "type": "string"
      },
      "comments": {
        "type": "array"
      }
    },
    "required": [
      "owner",
      "repo",
      "pullNumber",
      "event"
    ]
  },
  "GithubDraftReviewComment": {
    "type": "object",
    "properties": {
      "path": {
        "type": "string"
      },
      "position": {
        "type": "number"
      },
      "line": {
        "type": "number"
      },
      "side": {
        "type": "string"
      },
      "start_line": {
        "type": "number"
      },
      "start_side": {
        "type": "string"
      },
      "body": {
        "type": "string"
      }
    },
    "required": [
      "path",
      "body"
    ]
  },
  "GithubCreateRepositoryInput": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "private": {
        "type": "boolean"
      },
      "has_issues": {
        "type": "boolean"
      },
      "has_projects": {
        "type": "boolean"
      },
      "has_wiki": {
        "type": "boolean"
      },
      "auto_init": {
        "type": "boolean"
      },
      "gitignore_template": {
        "type": "string"
      },
      "license_template": {
        "type": "string"
      }
    },
    "required": [
      "name"
    ]
  },
  "GithubRepositoryInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      }
    },
    "required": [
      "owner",
      "repo"
    ]
  },
  "GithubIssueInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "issue_number": {
        "type": "number"
      }
    },
    "required": [
      "owner",
      "repo",
      "issue_number"
    ]
  },
  "GithubPullRequestInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "pullNumber": {
        "type": "number"
      }
    },
    "required": [
      "owner",
      "repo",
      "pullNumber"
    ]
  },
  "GithubListBranchesInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "protected": {
        "type": "boolean"
      },
      "per_page": {
        "type": "number"
      },
      "page": {
        "type": "number"
      }
    },
    "required": [
      "owner",
      "repo"
    ]
  },
  "GithubIssuesInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "state": {
        "type": "string"
      },
      "sort": {
        "type": "string"
      },
      "direction": {
        "type": "string"
      },
      "per_page": {
        "type": "number"
      },
      "page": {
        "type": "number"
      }
    },
    "required": [
      "owner",
      "repo"
    ]
  },
  "GithubListPullRequestsInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "state": {
        "type": "string"
      },
      "head": {
        "type": "string"
      },
      "base": {
        "type": "string"
      },
      "sort": {
        "type": "string"
      },
      "direction": {
        "type": "string"
      },
      "per_page": {
        "type": "number"
      },
      "page": {
        "type": "number"
      }
    },
    "required": [
      "owner",
      "repo"
    ]
  },
  "GithubMergePullRequestInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "pullNumber": {
        "type": "number"
      },
      "commit_title": {
        "type": "string"
      },
      "commit_message": {
        "type": "string"
      },
      "merge_method": {
        "type": "string"
      }
    },
    "required": [
      "owner",
      "repo",
      "pullNumber"
    ]
  },
  "GithubUpdateIssueInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "issue_number": {
        "type": "number"
      },
      "title": {
        "type": "string"
      },
      "body": {
        "type": "string"
      },
      "state": {
        "type": "string"
      },
      "assignees": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "labels": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "owner",
      "repo",
      "issue_number"
    ]
  },
  "GithubUpdatePullRequestInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "pullNumber": {
        "type": "number"
      },
      "title": {
        "type": "string"
      },
      "body": {
        "type": "string"
      },
      "state": {
        "type": "string"
      },
      "base": {
        "type": "string"
      },
      "maintainer_can_modify": {
        "type": "boolean"
      }
    },
    "required": [
      "owner",
      "repo",
      "pullNumber"
    ]
  },
  "GithubUpdatePullRequestBranchInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "pullNumber": {
        "type": "number"
      },
      "expectedHeadSha": {
        "type": "string"
      }
    },
    "required": [
      "owner",
      "repo",
      "pullNumber"
    ]
  },
  "GithubUpdateRepositoryInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "private": {
        "type": "boolean"
      },
      "has_issues": {
        "type": "boolean"
      },
      "has_projects": {
        "type": "boolean"
      },
      "has_wiki": {
        "type": "boolean"
      },
      "default_branch": {
        "type": "string"
      }
    },
    "required": [
      "owner",
      "repo"
    ]
  },
  "GithubWriteFileInput": {
    "type": "object",
    "properties": {
      "owner": {
        "type": "string"
      },
      "repo": {
        "type": "string"
      },
      "path": {
        "type": "string"
      },
      "message": {
        "type": "string"
      },
      "content": {
        "type": "string"
      },
      "sha": {
        "type": [
          "string",
          "undefined"
        ]
      }
    },
    "required": [
      "owner",
      "repo",
      "path",
      "message",
      "content",
      "sha"
    ]
  },
  "SlackAddReactionInput": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string"
      },
      "channel": {
        "type": "string"
      },
      "timestamp": {
        "type": "string"
      }
    },
    "required": [
      "name",
      "channel",
      "timestamp"
    ]
  },
  "SlackGetChannelHistoryInput": {
    "type": "object",
    "properties": {
      "channel": {
        "type": "string"
      },
      "limit": {
        "type": "number"
      },
      "latest": {
        "type": "string"
      },
      "oldest": {
        "type": "string"
      },
      "cursor": {
        "type": "string"
      }
    },
    "required": [
      "channel"
    ]
  },
  "SlackGetPermalinkInput": {
    "type": "object",
    "properties": {
      "channel": {
        "type": "string"
      },
      "message_ts": {
        "type": "string"
      }
    },
    "required": [
      "channel",
      "message_ts"
    ]
  },
  "SlackGetUserInfoInput": {
    "type": "object",
    "properties": {
      "user": {
        "type": "string"
      }
    },
    "required": [
      "user"
    ]
  },
  "SlackListChannelsInput": {
    "type": "object",
    "properties": {
      "types": {
        "type": "string"
      },
      "limit": {
        "type": "number"
      },
      "cursor": {
        "type": "string"
      }
    },
    "required": []
  },
  "SlackSearchMessagesInput": {
    "type": "object",
    "properties": {
      "query": {
        "type": "string"
      },
      "sort": {
        "type": "string"
      },
      "sort_dir": {
        "type": "string"
      },
      "count": {
        "type": "number"
      },
      "page": {
        "type": "number"
      }
    },
    "required": [
      "query"
    ]
  },
  "SlackSendMessageInput": {
    "type": "object",
    "properties": {
      "channel": {
        "type": "string"
      },
      "text": {
        "type": "string"
      },
      "thread_ts": {
        "type": "string"
      }
    },
    "required": [
      "channel",
      "text"
    ]
  },
  "SlackUpdateMessageInput": {
    "type": "object",
    "properties": {
      "channel": {
        "type": "string"
      },
      "ts": {
        "type": "string"
      },
      "text": {
        "type": "string"
      }
    },
    "required": [
      "channel",
      "ts",
      "text"
    ]
  },
  "GoogleCalendarEventInput": {
    "type": "object",
    "properties": {
      "summary": {
        "type": "string",
        "description": "Event title / name."
      },
      "description": {
        "type": "string",
        "description": "Contains e.g. the agenda or specifics of the meeting. Can contain HTML."
      },
      "location": {
        "type": "string",
        "description": "Free form text."
      },
      "start": {
        "type": "string",
        "description": "Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter."
      },
      "end": {
        "type": "string",
        "description": "Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter."
      },
      "timeZone": {
        "type": "string",
        "description": "An IANA Time Zone e.g. (Area/City)"
      },
      "attendees": {
        "type": "array",
        "items": {
          "type": "string"
        },
        "description": "A list of attendee email addresses."
      }
    },
    "required": [
      "summary",
      "start",
      "end"
    ]
  },
  "GoogleCalendarEventDeleteInput": {
    "type": "object",
    "properties": {
      "calendarId": {
        "type": "string",
        "description": "Calendar identifier. Use \"primary\" unless otherwise advised."
      },
      "eventId": {
        "type": "string",
        "description": "Event identifier."
      },
      "sendUpdates": {
        "type": "string",
        "description": "Whether to send notifications about the deletion of the event."
      }
    },
    "required": [
      "calendarId",
      "eventId"
    ]
  },
  "GoogleCalendarEventsInput": {
    "type": "object",
    "properties": {
      "calendarId": {
        "type": "string",
        "description": "Calendar identifier. Use \"primary\" unless otherwise advised."
      },
      "timeMin": {
        "type": "string",
        "description": "Lower bound (inclusive) for an event's end time to filter by. Defaults to now. ISO8601 string format."
      },
      "timeMax": {
        "type": "string",
        "description": "Upper bound (exclusive) for an event's start time to filter by. Defaults to unbounded. ISO8601 string format."
      },
      "maxResults": {
        "type": "number",
        "description": "Defaults to 250. Max 2500."
      },
      "pageToken": {
        "type": "string",
        "description": "Token as per a previous response to get another page of results."
      },
      "orderBy": {
        "type": "string"
      },
      "q": {
        "type": "string",
        "description": "Free text search terms to find events that match these terms."
      },
      "singleEvents": {
        "type": "boolean"
      },
      "timeZone": {
        "type": "string",
        "description": "Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar."
      }
    },
    "required": [
      "calendarId"
    ]
  },
  "GoogleCalendarEventUpdateInput": {
    "type": "object",
    "properties": {
      "calendarId": {
        "type": "string",
        "description": "Calendar identifier. Use \"primary\" unless otherwise advised."
      },
      "eventId": {
        "type": "string",
        "description": "Event identifier."
      },
      "sendUpdates": {
        "type": "string",
        "description": "Whether to send notifications about the event update."
      },
      "summary": {
        "type": "string",
        "description": "Event title / name."
      },
      "description": {
        "type": "string",
        "description": "Contains e.g. the agenda or specifics of the meeting. Can contain HTML."
      },
      "location": {
        "type": "string",
        "description": "Free form text."
      },
      "start": {
        "type": "string",
        "description": "Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter."
      },
      "end": {
        "type": "string",
        "description": "Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter."
      },
      "timeZone": {
        "type": "string",
        "description": "An IANA Time Zone e.g. (Area/City)"
      },
      "attendees": {
        "type": "array",
        "description": "A list of attendee email addresses."
      }
    },
    "required": [
      "calendarId",
      "eventId"
    ]
  },
  "GoogleCalendarAttendeeInput": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "email": {
        "type": "string"
      },
      "displayName": {
        "type": "string"
      },
      "responseStatus": {
        "type": "string"
      },
      "optional": {
        "type": "boolean"
      },
      "resource": {
        "type": "boolean"
      },
      "comment": {
        "type": "string"
      }
    },
    "required": [
      "email",
      "responseStatus"
    ]
  },
  "GmailDraftInput": {
    "type": "object",
    "properties": {
      "recipient": {
        "type": "string"
      },
      "subject": {
        "type": "string"
      },
      "body": {
        "type": "string"
      },
      "headers": {
        "type": "object"
      },
      "attachments": {
        "type": "array"
      }
    },
    "required": [
      "recipient",
      "subject"
    ]
  },
  "UrlAccessibleFile": {
    "type": "object",
    "properties": {
      "url": {
        "type": "string"
      },
      "authentication": {
        "type": {
          "$ref": "UrlAuthentication"
        }
      }
    },
    "required": [
      "url",
      "authentication"
    ]
  },
  "UrlAuthentication": {
    "type": "object",
    "properties": {
      "providerKey": {
        "type": "string"
      },
      "connectionId": {
        "type": "string"
      }
    },
    "required": [
      "providerKey",
      "connectionId"
    ]
  },
  "GmailReplyDraftInput": {
    "type": "object",
    "properties": {
      "sender": {
        "type": "string"
      },
      "subject": {
        "type": "string"
      },
      "body": {
        "type": "string"
      },
      "threadId": {
        "type": "string"
      },
      "messageId": {
        "type": "string"
      },
      "inReplyTo": {
        "type": "string"
      },
      "references": {
        "type": "string"
      },
      "date": {
        "type": "string"
      },
      "replyBody": {
        "type": "string"
      }
    },
    "required": [
      "sender",
      "subject",
      "body",
      "threadId",
      "messageId",
      "inReplyTo",
      "references",
      "date",
      "replyBody"
    ]
  },
  "GmailMessageIdInput": {
    "type": "object",
    "properties": {
      "messageId": {
        "type": "string"
      }
    },
    "required": [
      "messageId"
    ]
  },
  "GmailGetMessageInput": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "format": {
        "type": "string"
      }
    },
    "required": [
      "id"
    ]
  },
  "GmailListMessagesInput": {
    "type": "object",
    "properties": {
      "maxResults": {
        "type": "number"
      },
      "labelIds": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "q": {
        "type": "string"
      },
      "pageToken": {
        "type": "string"
      }
    },
    "required": []
  },
  "GmailModifyMessageLabelsInput": {
    "type": "object",
    "properties": {
      "messageId": {
        "type": "string"
      },
      "addLabelIds": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "removeLabelIds": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "messageId"
    ]
  },
  "GmailSendEmailInput": {
    "type": "object",
    "properties": {
      "to": {
        "type": "string"
      },
      "subject": {
        "type": "string"
      },
      "body": {
        "type": "string"
      },
      "from": {
        "type": "string"
      },
      "cc": {
        "type": "string"
      },
      "bcc": {
        "type": "string"
      },
      "attachments": {
        "type": "array"
      }
    },
    "required": [
      "to",
      "subject",
      "body"
    ]
  },
  "GmailAttachment": {
    "type": "object",
    "properties": {
      "filename": {
        "type": "string"
      },
      "content": {
        "type": "string"
      },
      "mimeType": {
        "type": "string"
      }
    },
    "required": [
      "filename",
      "content",
      "mimeType"
    ]
  },
  "DropboxCopyInput": {
    "type": "object",
    "properties": {
      "from_path": {
        "type": "string"
      },
      "to_path": {
        "type": "string"
      },
      "allow_shared_folder": {
        "type": "boolean"
      },
      "autorename": {
        "type": "boolean"
      }
    },
    "required": [
      "from_path",
      "to_path"
    ]
  },
  "DropboxCreateFolderInput": {
    "type": "object",
    "properties": {
      "path": {
        "type": "string"
      },
      "autorename": {
        "type": "boolean"
      }
    },
    "required": [
      "path"
    ]
  },
  "DropboxDeleteInput": {
    "type": "object",
    "properties": {
      "path": {
        "type": "string"
      }
    },
    "required": [
      "path"
    ]
  },
  "Anonymous_dropbox_action_fetchfile_input": {
    "type": "object",
    "properties": {
      "input": {
        "type": "string"
      }
    },
    "required": [
      "input"
    ]
  },
  "DropboxGetFileInput": {
    "type": "object",
    "properties": {
      "path": {
        "type": "string"
      }
    },
    "required": [
      "path"
    ]
  },
  "DropboxListFilesInput": {
    "type": "object",
    "properties": {
      "path": {
        "type": "string"
      },
      "recursive": {
        "type": "boolean"
      },
      "limit": {
        "type": "number"
      },
      "include_deleted": {
        "type": "boolean"
      }
    },
    "required": [
      "path"
    ]
  },
  "DropboxMoveInput": {
    "type": "object",
    "properties": {
      "from_path": {
        "type": "string"
      },
      "to_path": {
        "type": "string"
      },
      "allow_shared_folder": {
        "type": "boolean"
      },
      "autorename": {
        "type": "boolean"
      },
      "allow_ownership_transfer": {
        "type": "boolean"
      }
    },
    "required": [
      "from_path",
      "to_path"
    ]
  },
  "DropboxSearchInput": {
    "type": "object",
    "properties": {
      "query": {
        "type": "string"
      },
      "path": {
        "type": "string"
      },
      "max_results": {
        "type": "number"
      },
      "mode": {
        "type": "string"
      }
    },
    "required": [
      "query"
    ]
  },
  "DropboxUploadFileInput": {
    "type": "object",
    "properties": {
      "path": {
        "type": "string"
      },
      "content": {
        "type": "string"
      },
      "encoding": {
        "type": "string"
      },
      "mode": {
        "type": "string"
      },
      "autorename": {
        "type": "boolean"
      },
      "mute": {
        "type": "boolean"
      }
    },
    "required": [
      "path",
      "content"
    ]
  },
  "NotionCreateDatabaseInput": {
    "type": "object",
    "properties": {
      "parentId": {
        "type": "string"
      },
      "title": {
        "type": "array"
      },
      "properties": {
        "type": "object"
      }
    },
    "required": [
      "parentId",
      "title",
      "properties"
    ]
  },
  "NotionRichText": {
    "type": "object",
    "properties": {
      "type": {
        "type": "string"
      },
      "text": {
        "type": {
          "$ref": "NotionRichTextContent"
        }
      },
      "annotations": {
        "type": {
          "$ref": "NotionRichTextAnnotations"
        }
      },
      "plain_text": {
        "type": "string"
      },
      "href": {
        "type": [
          "string",
          null
        ]
      }
    },
    "required": []
  },
  "NotionRichTextContent": {
    "type": "object",
    "properties": {
      "content": {
        "type": "string"
      },
      "link": {
        "type": [
          "Record<string, any>",
          null
        ]
      }
    },
    "required": []
  },
  "NotionRichTextAnnotations": {
    "type": "object",
    "properties": {
      "bold": {
        "type": "boolean"
      },
      "italic": {
        "type": "boolean"
      },
      "strikethrough": {
        "type": "boolean"
      },
      "underline": {
        "type": "boolean"
      },
      "code": {
        "type": "boolean"
      },
      "color": {
        "type": "string"
      }
    },
    "required": []
  },
  "NotionCreatePageInput": {
    "type": "object",
    "properties": {
      "parentId": {
        "type": "string"
      },
      "parentType": {
        "type": "string"
      },
      "properties": {
        "type": "object"
      },
      "children": {
        "type": "array",
        "items": {
          "type": "object"
        }
      }
    },
    "required": [
      "parentId",
      "properties"
    ]
  },
  "NotionGetDatabaseInput": {
    "type": "object",
    "properties": {
      "databaseId": {
        "type": "string"
      }
    },
    "required": [
      "databaseId"
    ]
  },
  "NotionGetPageInput": {
    "type": "object",
    "properties": {
      "pageId": {
        "type": "string"
      }
    },
    "required": [
      "pageId"
    ]
  },
  "NotionQueryDatabaseInput": {
    "type": "object",
    "properties": {
      "databaseId": {
        "type": "string"
      },
      "filter": {
        "type": "object"
      },
      "sorts": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "start_cursor": {
        "type": "string"
      },
      "page_size": {
        "type": "number"
      }
    },
    "required": [
      "databaseId"
    ]
  },
  "NotionSearchInput": {
    "type": "object",
    "properties": {
      "query": {
        "type": "string"
      },
      "sort": {
        "type": {
          "$ref": "NotionSort"
        }
      },
      "filter": {
        "type": {
          "$ref": "NotionFilter"
        }
      },
      "start_cursor": {
        "type": "string"
      },
      "page_size": {
        "type": "number"
      }
    },
    "required": []
  },
  "NotionSort": {
    "type": "object",
    "properties": {
      "direction": {
        "type": "string"
      },
      "timestamp": {
        "type": "string"
      }
    },
    "required": []
  },
  "NotionFilter": {
    "type": "object",
    "properties": {
      "value": {
        "type": "string"
      },
      "property": {
        "type": "string"
      }
    },
    "required": []
  },
  "NotionUpdateDatabaseInput": {
    "type": "object",
    "properties": {
      "databaseId": {
        "type": "string"
      },
      "title": {
        "type": "array"
      },
      "description": {
        "type": "array"
      },
      "properties": {
        "type": "object"
      },
      "archived": {
        "type": "boolean"
      }
    },
    "required": [
      "databaseId"
    ]
  },
  "NotionUpdatePageInput": {
    "type": "object",
    "properties": {
      "pageId": {
        "type": "string"
      },
      "properties": {
        "type": "object"
      },
      "archived": {
        "type": "boolean"
      },
      "icon": {
        "type": [
          "Record<string, any>",
          null
        ]
      },
      "cover": {
        "type": [
          "Record<string, any>",
          null
        ]
      }
    },
    "required": [
      "pageId"
    ]
  },
  "GoogleDocsCreateDocumentInput": {
    "type": "object",
    "properties": {
      "title": {
        "type": "string"
      }
    },
    "required": []
  },
  "GoogleDocsGetDocumentInput": {
    "type": "object",
    "properties": {
      "documentId": {
        "type": "string"
      }
    },
    "required": [
      "documentId"
    ]
  },
  "GoogleDocsUpdateDocumentInput": {
    "type": "object",
    "properties": {
      "documentId": {
        "type": "string"
      },
      "requests": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "writeControl": {
        "type": "object"
      }
    },
    "required": [
      "documentId",
      "requests"
    ]
  },
  "LinearCreateIssueInput": {
    "type": "object",
    "properties": {
      "teamId": {
        "type": "string"
      },
      "title": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "stateId": {
        "type": "string"
      },
      "assigneeId": {
        "type": "string"
      },
      "priority": {
        "type": "number"
      },
      "projectId": {
        "type": "string"
      },
      "labelIds": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "teamId",
      "title"
    ]
  },
  "LinearCreateProjectInput": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "icon": {
        "type": "string"
      },
      "color": {
        "type": "string"
      },
      "teamIds": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "name",
      "teamIds"
    ]
  },
  "LinearIssueInput": {
    "type": "object",
    "properties": {
      "issueId": {
        "type": "string"
      }
    },
    "required": [
      "issueId"
    ]
  },
  "LinearProjectInput": {
    "type": "object",
    "properties": {
      "projectId": {
        "type": "string"
      }
    },
    "required": [
      "projectId"
    ]
  },
  "LinearTeamInput": {
    "type": "object",
    "properties": {
      "teamId": {
        "type": "string"
      }
    },
    "required": [
      "teamId"
    ]
  },
  "LinearIssuesInput": {
    "type": "object",
    "properties": {
      "teamId": {
        "type": "string"
      },
      "projectId": {
        "type": "string"
      },
      "states": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "assigneeId": {
        "type": "string"
      },
      "priority": {
        "type": "number"
      },
      "sortBy": {
        "type": "string"
      },
      "sortOrder": {
        "type": "string"
      },
      "limit": {
        "type": "number"
      },
      "first": {
        "type": "number"
      },
      "after": {
        "type": "string"
      }
    },
    "required": []
  },
  "LinearProjectsInput": {
    "type": "object",
    "properties": {
      "first": {
        "type": "number"
      },
      "after": {
        "type": "string"
      }
    },
    "required": []
  },
  "LinearTeamsInput": {
    "type": "object",
    "properties": {
      "first": {
        "type": "number"
      },
      "after": {
        "type": "string"
      }
    },
    "required": []
  },
  "LinearUpdateIssueInput": {
    "type": "object",
    "properties": {
      "issueId": {
        "type": "string"
      },
      "title": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "stateId": {
        "type": "string"
      },
      "assigneeId": {
        "type": "string"
      },
      "priority": {
        "type": "number"
      },
      "projectId": {
        "type": "string"
      },
      "labelIds": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "issueId"
    ]
  },
  "LinearUpdateProjectInput": {
    "type": "object",
    "properties": {
      "projectId": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "icon": {
        "type": "string"
      },
      "color": {
        "type": "string"
      },
      "state": {
        "type": "string"
      },
      "teamIds": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "projectId"
    ]
  },
  "GoogleSheetCreateInput": {
    "type": "object",
    "properties": {
      "title": {
        "type": "string"
      },
      "sheets": {
        "type": "array"
      }
    },
    "required": [
      "title"
    ]
  },
  "GoogleSheetTab": {
    "type": "object",
    "properties": {
      "title": {
        "type": "string"
      },
      "data": {
        "type": {
          "$ref": "SheetData"
        }
      }
    },
    "required": [
      "title"
    ]
  },
  "SheetData": {
    "type": "object",
    "properties": {
      "rows": {
        "type": "array"
      }
    },
    "required": [
      "rows"
    ]
  },
  "SheetRow": {
    "type": "object",
    "properties": {
      "cells": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "cells"
    ]
  },
  "SpreadsheetId": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      }
    },
    "required": [
      "id"
    ]
  },
  "GoogleSheetUpdateInput": {
    "type": "object",
    "properties": {
      "spreadsheetId": {
        "type": "string"
      },
      "updates": {
        "type": "array"
      }
    },
    "required": [
      "spreadsheetId",
      "updates"
    ]
  },
  "SheetUpdate": {
    "type": "object",
    "properties": {
      "sheetId": {
        "type": "number"
      },
      "sheetName": {
        "type": "string"
      },
      "range": {
        "type": "string"
      },
      "startRow": {
        "type": "number"
      },
      "startColumn": {
        "type": "number"
      },
      "data": {
        "type": {
          "$ref": "SheetData"
        }
      }
    },
    "required": [
      "data"
    ]
  },
  "IdEntity": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      }
    },
    "required": [
      "id"
    ]
  },
  "FolderContentInput": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "cursor": {
        "type": "string"
      }
    },
    "required": []
  },
  "ListDocumentsInput": {
    "type": "object",
    "properties": {
      "folderId": {
        "type": "string"
      },
      "mimeType": {
        "type": "string"
      },
      "pageSize": {
        "type": "number"
      },
      "pageToken": {
        "type": "string"
      },
      "orderBy": {
        "type": "string"
      }
    },
    "required": []
  },
  "UploadFileInput": {
    "type": "object",
    "properties": {
      "content": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "mimeType": {
        "type": "string"
      },
      "folderId": {
        "type": [
          "string",
          "undefined"
        ]
      },
      "description": {
        "type": [
          "string",
          "undefined"
        ]
      },
      "isBase64": {
        "type": [
          "boolean",
          "undefined"
        ]
      }
    },
    "required": [
      "content",
      "name",
      "mimeType"
    ]
  },
  "LinkedInPostInput": {
    "type": "object",
    "properties": {
      "text": {
        "type": "string"
      },
      "visibility": {
        "type": "string"
      }
    },
    "required": [
      "text",
      "visibility"
    ]
  },
  "XSocialPostInput": {
    "type": "object",
    "properties": {
      "text": {
        "type": "string"
      },
      "reply_to": {
        "type": "string"
      },
      "quote": {
        "type": "string"
      }
    },
    "required": [
      "text"
    ]
  }
};

export const ACTION_OUTPUT_MODELS_JSON_SCHEMA = {
  "HarvestTimeEntry": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "spent_date": {
        "type": "string"
      },
      "hours": {
        "type": "number"
      },
      "notes": {
        "type": "string"
      },
      "is_locked": {
        "type": "boolean"
      },
      "is_running": {
        "type": "boolean"
      },
      "is_billed": {
        "type": "boolean"
      },
      "timer_started_at": {
        "type": [
          "string",
          null
        ]
      },
      "started_time": {
        "type": [
          "string",
          null
        ]
      },
      "ended_time": {
        "type": [
          "string",
          null
        ]
      },
      "user": {
        "type": {
          "$ref": "HarvestUser"
        }
      },
      "client": {
        "type": {
          "$ref": "HarvestClientInTimeEntry"
        }
      },
      "project": {
        "type": {
          "$ref": "ProjectReference"
        }
      },
      "task": {
        "type": {
          "$ref": "HarvestTaskInTimeEntry"
        }
      },
      "created_at": {
        "type": "string"
      },
      "updated_at": {
        "type": "string"
      },
      "hours_without_timer": {
        "type": "number"
      },
      "rounded_hours": {
        "type": "number"
      },
      "locked_reason": {
        "type": [
          "string",
          null
        ]
      },
      "is_closed": {
        "type": "boolean"
      },
      "billable": {
        "type": "boolean"
      },
      "budgeted": {
        "type": "boolean"
      },
      "billable_rate": {
        "type": [
          "number",
          null
        ]
      },
      "cost_rate": {
        "type": [
          "number",
          null
        ]
      },
      "user_assignment": {
        "type": "object"
      },
      "task_assignment": {
        "type": "object"
      },
      "invoice": {
        "type": [
          "Record<string, any>",
          null
        ]
      },
      "external_reference": {
        "type": [
          "HarvestExternalReferenceInput",
          null
        ]
      }
    },
    "required": [
      "id",
      "spent_date",
      "hours",
      "is_locked",
      "is_running",
      "is_billed",
      "user",
      "client",
      "project",
      "task",
      "created_at",
      "updated_at"
    ]
  },
  "HarvestUser": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      },
      "email": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name"
    ]
  },
  "HarvestClientInTimeEntry": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      },
      "currency": {
        "type": "string"
      },
      "is_active": {
        "type": "boolean"
      }
    },
    "required": [
      "id",
      "name"
    ]
  },
  "ProjectReference": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      },
      "code": {
        "type": [
          "string",
          null
        ]
      },
      "is_active": {
        "type": "boolean"
      },
      "is_billable": {
        "type": "boolean"
      }
    },
    "required": [
      "id",
      "name"
    ]
  },
  "HarvestTaskInTimeEntry": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      },
      "is_active": {
        "type": "boolean"
      },
      "billable_by_default": {
        "type": "boolean"
      },
      "is_default": {
        "type": "boolean"
      },
      "created_at": {
        "type": "string"
      },
      "updated_at": {
        "type": "string"
      },
      "default_hourly_rate": {
        "type": [
          "number",
          null
        ]
      }
    },
    "required": [
      "id",
      "name"
    ]
  },
  "HarvestClient": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      },
      "is_active": {
        "type": "boolean"
      },
      "address": {
        "type": [
          "string",
          null
        ]
      },
      "statement_key": {
        "type": "string"
      },
      "created_at": {
        "type": "string"
      },
      "updated_at": {
        "type": "string"
      },
      "currency": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "is_active"
    ]
  },
  "HarvestProject": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      },
      "code": {
        "type": [
          "string",
          null
        ]
      },
      "client": {
        "type": {
          "$ref": "HarvestClientReference"
        }
      },
      "is_active": {
        "type": "boolean"
      },
      "is_billable": {
        "type": "boolean"
      },
      "is_fixed_fee": {
        "type": "boolean"
      },
      "bill_by": {
        "type": "string"
      },
      "budget": {
        "type": [
          "number",
          null
        ]
      },
      "budget_by": {
        "type": "string"
      },
      "budget_is_monthly": {
        "type": "boolean"
      },
      "notify_when_over_budget": {
        "type": "boolean"
      },
      "over_budget_notification_percentage": {
        "type": "number"
      },
      "show_budget_to_all": {
        "type": "boolean"
      },
      "created_at": {
        "type": "string"
      },
      "updated_at": {
        "type": "string"
      },
      "starts_on": {
        "type": [
          "string",
          null
        ]
      },
      "ends_on": {
        "type": [
          "string",
          null
        ]
      },
      "over_budget_notification_date": {
        "type": [
          "string",
          null
        ]
      },
      "notes": {
        "type": [
          "string",
          null
        ]
      },
      "cost_budget": {
        "type": [
          "number",
          null
        ]
      },
      "cost_budget_include_expenses": {
        "type": "boolean"
      },
      "hourly_rate": {
        "type": [
          "number",
          null
        ]
      },
      "fee": {
        "type": [
          "number",
          null
        ]
      }
    },
    "required": [
      "id",
      "name",
      "client",
      "is_active",
      "is_billable",
      "is_fixed_fee",
      "bill_by",
      "budget_by",
      "budget_is_monthly",
      "notify_when_over_budget",
      "show_budget_to_all",
      "created_at",
      "updated_at",
      "cost_budget_include_expenses"
    ]
  },
  "HarvestClientReference": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      },
      "currency": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "currency"
    ]
  },
  "HarvestDeleteProjectOutput": {
    "type": "object",
    "properties": {
      "success": {
        "type": "boolean"
      },
      "message": {
        "type": "string"
      }
    },
    "required": [
      "success",
      "message"
    ]
  },
  "HarvestDeleteTimeEntryOutput": {
    "type": "object",
    "properties": {
      "success": {
        "type": "boolean"
      },
      "message": {
        "type": "string"
      }
    },
    "required": [
      "success",
      "message"
    ]
  },
  "HarvestClientList": {
    "type": "object",
    "properties": {
      "clients": {
        "type": "array"
      },
      "per_page": {
        "type": "number"
      },
      "total_pages": {
        "type": "number"
      },
      "total_entries": {
        "type": "number"
      },
      "next_page": {
        "type": [
          "number",
          null
        ]
      },
      "previous_page": {
        "type": [
          "number",
          null
        ]
      },
      "page": {
        "type": "number"
      },
      "links": {
        "type": "object"
      }
    },
    "required": [
      "clients",
      "per_page",
      "total_pages",
      "total_entries"
    ]
  },
  "HarvestProjectList": {
    "type": "object",
    "properties": {
      "projects": {
        "type": "array"
      },
      "per_page": {
        "type": "number"
      },
      "total_pages": {
        "type": "number"
      },
      "total_entries": {
        "type": "number"
      },
      "next_page": {
        "type": [
          "number",
          null
        ]
      },
      "previous_page": {
        "type": [
          "number",
          null
        ]
      },
      "page": {
        "type": "number"
      },
      "links": {
        "type": {
          "$ref": "HarvestPaginationLinks"
        }
      }
    },
    "required": [
      "projects",
      "per_page",
      "total_pages",
      "total_entries"
    ]
  },
  "HarvestPaginationLinks": {
    "type": "object",
    "properties": {
      "first": {
        "type": "string"
      },
      "next": {
        "type": [
          "string",
          null
        ]
      },
      "previous": {
        "type": [
          "string",
          null
        ]
      },
      "last": {
        "type": "string"
      }
    },
    "required": []
  },
  "HarvestProjectTaskList": {
    "type": "object",
    "properties": {
      "task_assignments": {
        "type": "array"
      },
      "per_page": {
        "type": "number"
      },
      "total_pages": {
        "type": "number"
      },
      "total_entries": {
        "type": "number"
      },
      "next_page": {
        "type": [
          "number",
          null
        ]
      },
      "previous_page": {
        "type": [
          "number",
          null
        ]
      },
      "page": {
        "type": "number"
      },
      "links": {
        "type": {
          "$ref": "PaginationLinks"
        }
      }
    },
    "required": [
      "task_assignments"
    ]
  },
  "HarvestProjectTask": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "billable": {
        "type": "boolean"
      },
      "is_active": {
        "type": "boolean"
      },
      "created_at": {
        "type": "string"
      },
      "updated_at": {
        "type": "string"
      },
      "hourly_rate": {
        "type": [
          "number",
          null
        ]
      },
      "budget": {
        "type": [
          "number",
          null
        ]
      },
      "project": {
        "type": {
          "$ref": "ProjectReference"
        }
      },
      "task": {
        "type": {
          "$ref": "TaskInAssignment"
        }
      }
    },
    "required": [
      "id",
      "billable",
      "is_active",
      "created_at",
      "updated_at",
      "task"
    ]
  },
  "TaskInAssignment": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name"
    ]
  },
  "PaginationLinks": {
    "type": "object",
    "properties": {
      "first": {
        "type": "string"
      },
      "next": {
        "type": [
          "string",
          null
        ]
      },
      "previous": {
        "type": [
          "string",
          null
        ]
      },
      "last": {
        "type": "string"
      }
    },
    "required": []
  },
  "HarvestTaskList": {
    "type": "object",
    "properties": {
      "tasks": {
        "type": "array"
      },
      "per_page": {
        "type": "number"
      },
      "total_pages": {
        "type": "number"
      },
      "total_entries": {
        "type": "number"
      },
      "next_page": {
        "type": [
          "number",
          null
        ]
      },
      "previous_page": {
        "type": [
          "number",
          null
        ]
      },
      "page": {
        "type": "number"
      },
      "links": {
        "type": "object"
      }
    },
    "required": [
      "tasks",
      "per_page",
      "total_pages",
      "total_entries",
      "page"
    ]
  },
  "HarvestTask": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      },
      "is_active": {
        "type": "boolean"
      },
      "billable_by_default": {
        "type": "boolean"
      },
      "is_default": {
        "type": "boolean"
      },
      "created_at": {
        "type": "string"
      },
      "updated_at": {
        "type": "string"
      },
      "default_hourly_rate": {
        "type": [
          "number",
          null
        ]
      }
    },
    "required": [
      "id",
      "name",
      "is_active",
      "billable_by_default",
      "created_at",
      "updated_at"
    ]
  },
  "HarvestTimeEntryList": {
    "type": "object",
    "properties": {
      "time_entries": {
        "type": "array"
      },
      "per_page": {
        "type": "number"
      },
      "total_pages": {
        "type": "number"
      },
      "total_entries": {
        "type": "number"
      },
      "next_page": {
        "type": [
          "number",
          null
        ]
      },
      "previous_page": {
        "type": [
          "number",
          null
        ]
      },
      "page": {
        "type": "number"
      },
      "links": {
        "type": "object"
      }
    },
    "required": [
      "time_entries",
      "per_page",
      "total_pages",
      "total_entries"
    ]
  },
  "GithubPullRequestComment": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "node_id": {
        "type": "string"
      },
      "url": {
        "type": "string"
      },
      "pull_request_review_id": {
        "type": "number"
      },
      "diff_hunk": {
        "type": "string"
      },
      "path": {
        "type": "string"
      },
      "position": {
        "type": "number"
      },
      "original_position": {
        "type": "number"
      },
      "commit_id": {
        "type": "string"
      },
      "original_commit_id": {
        "type": "string"
      },
      "user": {
        "type": {
          "$ref": "GithubIssueCreator"
        }
      },
      "body": {
        "type": "string"
      },
      "created_at": {
        "type": "string"
      },
      "updated_at": {
        "type": "string"
      },
      "html_url": {
        "type": "string"
      },
      "pull_request_url": {
        "type": "string"
      },
      "author_association": {
        "type": "string"
      },
      "_links": {
        "type": "object"
      },
      "reactions": {
        "type": {
          "$ref": "GithubReactions"
        }
      },
      "start_line": {
        "type": [
          "number",
          null
        ]
      },
      "original_start_line": {
        "type": [
          "number",
          null
        ]
      },
      "start_side": {
        "type": [
          "string",
          null
        ]
      },
      "line": {
        "type": "number"
      },
      "original_line": {
        "type": "number"
      },
      "side": {
        "type": "string"
      },
      "in_reply_to_id": {
        "type": "number"
      },
      "subject_type": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "node_id",
      "url",
      "pull_request_review_id",
      "diff_hunk",
      "path",
      "position",
      "original_position",
      "commit_id",
      "original_commit_id",
      "user",
      "body",
      "created_at",
      "updated_at",
      "html_url",
      "pull_request_url",
      "author_association",
      "_links",
      "reactions",
      "start_line",
      "original_start_line",
      "start_side",
      "line",
      "original_line",
      "side",
      "subject_type"
    ]
  },
  "GithubIssueCreator": {
    "type": "object",
    "properties": {
      "login": {
        "type": "string"
      },
      "id": {
        "type": "number"
      },
      "node_id": {
        "type": "string"
      },
      "avatar_url": {
        "type": "string"
      },
      "gravatar_id": {
        "type": "string"
      },
      "url": {
        "type": "string"
      },
      "html_url": {
        "type": "string"
      },
      "followers_url": {
        "type": "string"
      },
      "following_url": {
        "type": "string"
      },
      "gists_url": {
        "type": "string"
      },
      "starred_url": {
        "type": "string"
      },
      "subscriptions_url": {
        "type": "string"
      },
      "organizations_url": {
        "type": "string"
      },
      "repos_url": {
        "type": "string"
      },
      "events_url": {
        "type": "string"
      },
      "received_events_url": {
        "type": "string"
      },
      "type": {
        "type": "string"
      },
      "user_view_type": {
        "type": "string"
      },
      "site_admin": {
        "type": "boolean"
      }
    },
    "required": [
      "login",
      "id",
      "node_id",
      "avatar_url",
      "gravatar_id",
      "url",
      "html_url",
      "followers_url",
      "following_url",
      "gists_url",
      "starred_url",
      "subscriptions_url",
      "organizations_url",
      "repos_url",
      "events_url",
      "received_events_url",
      "type",
      "user_view_type",
      "site_admin"
    ]
  },
  "GithubReactions": {
    "type": "object",
    "properties": {
      "url": {
        "type": "string"
      },
      "total_count": {
        "type": "number"
      },
      "+1": {
        "type": "number"
      },
      "-1": {
        "type": "number"
      },
      "laugh": {
        "type": "number"
      },
      "hooray": {
        "type": "number"
      },
      "confused": {
        "type": "number"
      },
      "heart": {
        "type": "number"
      },
      "rocket": {
        "type": "number"
      },
      "eyes": {
        "type": "number"
      }
    },
    "required": [
      "url",
      "total_count",
      "+1",
      "-1",
      "laugh",
      "hooray",
      "confused",
      "heart",
      "rocket",
      "eyes"
    ]
  },
  "GithubIssue": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "node_id": {
        "type": "string"
      },
      "url": {
        "type": "string"
      },
      "repository_url": {
        "type": "string"
      },
      "labels_url": {
        "type": "string"
      },
      "comments_url": {
        "type": "string"
      },
      "events_url": {
        "type": "string"
      },
      "html_url": {
        "type": "string"
      },
      "number": {
        "type": "number"
      },
      "title": {
        "type": "string"
      },
      "state": {
        "type": "string"
      },
      "locked": {
        "type": "boolean"
      },
      "body": {
        "type": [
          "string",
          null
        ]
      },
      "user": {
        "type": [
          "GithubIssueCreatorLite",
          "GithubIssueCreator"
        ]
      },
      "labels": {
        "type": "array"
      },
      "assignee": {
        "type": [
          "Record<string, any>",
          null
        ]
      },
      "assignees": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "milestone": {
        "type": [
          "string",
          null
        ]
      },
      "comments": {
        "type": "number"
      },
      "created_at": {
        "type": "string"
      },
      "updated_at": {
        "type": "string"
      },
      "closed_at": {
        "type": [
          "string",
          null
        ]
      },
      "author_association": {
        "type": "string"
      },
      "active_lock_reason": {
        "type": [
          "string",
          null
        ]
      },
      "sub_issues_summary": {
        "type": {
          "$ref": "GithubSubIssuesSummary"
        }
      },
      "closed_by": {
        "type": [
          "GithubIssueCreator",
          null
        ]
      },
      "reactions": {
        "type": {
          "$ref": "GithubReactions"
        }
      },
      "timeline_url": {
        "type": "string"
      },
      "performed_via_github_app": {
        "type": [
          "Record<string, any>",
          null
        ]
      },
      "state_reason": {
        "type": [
          "string",
          null
        ]
      }
    },
    "required": [
      "id",
      "node_id",
      "url",
      "repository_url",
      "labels_url",
      "comments_url",
      "events_url",
      "html_url",
      "number",
      "title",
      "state",
      "locked",
      "body",
      "user",
      "labels",
      "assignee",
      "assignees",
      "milestone",
      "comments",
      "created_at",
      "updated_at",
      "closed_at",
      "author_association",
      "active_lock_reason",
      "sub_issues_summary",
      "closed_by",
      "reactions",
      "timeline_url",
      "performed_via_github_app",
      "state_reason"
    ]
  },
  "GithubIssueLabel": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      },
      "color": {
        "type": "string"
      },
      "description": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "color",
      "description"
    ]
  },
  "GithubSubIssuesSummary": {
    "type": "object",
    "properties": {
      "total": {
        "type": "number"
      },
      "completed": {
        "type": "number"
      },
      "percent_completed": {
        "type": "number"
      }
    },
    "required": [
      "total",
      "completed",
      "percent_completed"
    ]
  },
  "GithubRepository": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "node_id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "full_name": {
        "type": "string"
      },
      "private": {
        "type": "boolean"
      },
      "owner": {
        "type": {
          "$ref": "GithubIssueCreator"
        }
      },
      "html_url": {
        "type": "string"
      },
      "description": {
        "type": [
          "string",
          null
        ]
      },
      "fork": {
        "type": "boolean"
      },
      "url": {
        "type": "string"
      },
      "forks_url": {
        "type": "string"
      },
      "keys_url": {
        "type": "string"
      },
      "collaborators_url": {
        "type": "string"
      },
      "teams_url": {
        "type": "string"
      },
      "hooks_url": {
        "type": "string"
      },
      "issue_events_url": {
        "type": "string"
      },
      "events_url": {
        "type": "string"
      },
      "assignees_url": {
        "type": "string"
      },
      "branches_url": {
        "type": "string"
      },
      "tags_url": {
        "type": "string"
      },
      "blobs_url": {
        "type": "string"
      },
      "git_tags_url": {
        "type": "string"
      },
      "git_refs_url": {
        "type": "string"
      },
      "trees_url": {
        "type": "string"
      },
      "statuses_url": {
        "type": "string"
      },
      "languages_url": {
        "type": "string"
      },
      "stargazers_url": {
        "type": "string"
      },
      "contributors_url": {
        "type": "string"
      },
      "subscribers_url": {
        "type": "string"
      },
      "subscription_url": {
        "type": "string"
      },
      "commits_url": {
        "type": "string"
      },
      "git_commits_url": {
        "type": "string"
      },
      "comments_url": {
        "type": "string"
      },
      "issue_comment_url": {
        "type": "string"
      },
      "contents_url": {
        "type": "string"
      },
      "compare_url": {
        "type": "string"
      },
      "merges_url": {
        "type": "string"
      },
      "archive_url": {
        "type": "string"
      },
      "downloads_url": {
        "type": "string"
      },
      "issues_url": {
        "type": "string"
      },
      "pulls_url": {
        "type": "string"
      },
      "milestones_url": {
        "type": "string"
      },
      "notifications_url": {
        "type": "string"
      },
      "labels_url": {
        "type": "string"
      },
      "releases_url": {
        "type": "string"
      },
      "deployments_url": {
        "type": "string"
      },
      "created_at": {
        "type": "string"
      },
      "updated_at": {
        "type": "string"
      },
      "pushed_at": {
        "type": "string"
      },
      "git_url": {
        "type": "string"
      },
      "ssh_url": {
        "type": "string"
      },
      "clone_url": {
        "type": "string"
      },
      "svn_url": {
        "type": "string"
      },
      "homepage": {
        "type": [
          "string",
          null
        ]
      },
      "size": {
        "type": "number"
      },
      "stargazers_count": {
        "type": "number"
      },
      "watchers_count": {
        "type": "number"
      },
      "language": {
        "type": [
          "string",
          null
        ]
      },
      "has_issues": {
        "type": "boolean"
      },
      "has_projects": {
        "type": "boolean"
      },
      "has_downloads": {
        "type": "boolean"
      },
      "has_wiki": {
        "type": "boolean"
      },
      "has_pages": {
        "type": "boolean"
      },
      "has_discussions": {
        "type": "boolean"
      },
      "forks_count": {
        "type": "number"
      },
      "mirror_url": {
        "type": [
          "string",
          null
        ]
      },
      "archived": {
        "type": "boolean"
      },
      "disabled": {
        "type": "boolean"
      },
      "open_issues_count": {
        "type": "number"
      },
      "license": {
        "type": [
          "string",
          null
        ]
      },
      "allow_forking": {
        "type": "boolean"
      },
      "is_template": {
        "type": "boolean"
      },
      "web_commit_signoff_required": {
        "type": "boolean"
      },
      "topics": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "visibility": {
        "type": "string"
      },
      "forks": {
        "type": "number"
      },
      "open_issues": {
        "type": "number"
      },
      "watchers": {
        "type": "number"
      },
      "default_branch": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "node_id",
      "name",
      "full_name",
      "private",
      "owner",
      "html_url",
      "description",
      "fork",
      "url",
      "forks_url",
      "keys_url",
      "collaborators_url",
      "teams_url",
      "hooks_url",
      "issue_events_url",
      "events_url",
      "assignees_url",
      "branches_url",
      "tags_url",
      "blobs_url",
      "git_tags_url",
      "git_refs_url",
      "trees_url",
      "statuses_url",
      "languages_url",
      "stargazers_url",
      "contributors_url",
      "subscribers_url",
      "subscription_url",
      "commits_url",
      "git_commits_url",
      "comments_url",
      "issue_comment_url",
      "contents_url",
      "compare_url",
      "merges_url",
      "archive_url",
      "downloads_url",
      "issues_url",
      "pulls_url",
      "milestones_url",
      "notifications_url",
      "labels_url",
      "releases_url",
      "deployments_url",
      "created_at",
      "updated_at",
      "pushed_at",
      "git_url",
      "ssh_url",
      "clone_url",
      "svn_url",
      "homepage",
      "size",
      "stargazers_count",
      "watchers_count",
      "language",
      "has_issues",
      "has_projects",
      "has_downloads",
      "has_wiki",
      "has_pages",
      "has_discussions",
      "forks_count",
      "mirror_url",
      "archived",
      "disabled",
      "open_issues_count",
      "license",
      "allow_forking",
      "is_template",
      "web_commit_signoff_required",
      "topics",
      "visibility",
      "forks",
      "open_issues",
      "watchers",
      "default_branch"
    ]
  },
  "GithubPullRequest": {
    "type": "object",
    "properties": {
      "url": {
        "type": "string"
      },
      "id": {
        "type": "number"
      },
      "node_id": {
        "type": "string"
      },
      "html_url": {
        "type": "string"
      },
      "diff_url": {
        "type": "string"
      },
      "patch_url": {
        "type": "string"
      },
      "issue_url": {
        "type": "string"
      },
      "number": {
        "type": "number"
      },
      "state": {
        "type": "string"
      },
      "locked": {
        "type": "boolean"
      },
      "title": {
        "type": "string"
      },
      "user": {
        "type": {
          "$ref": "GithubIssueCreator"
        }
      },
      "body": {
        "type": [
          "string",
          null
        ]
      },
      "created_at": {
        "type": "string"
      },
      "updated_at": {
        "type": "string"
      },
      "closed_at": {
        "type": [
          "string",
          null
        ]
      },
      "merged_at": {
        "type": [
          "string",
          null
        ]
      },
      "merge_commit_sha": {
        "type": [
          "string",
          null
        ]
      },
      "assignee": {
        "type": [
          "GithubIssueAssignee",
          null
        ]
      },
      "assignees": {
        "type": "array"
      },
      "requested_reviewers": {
        "type": "array"
      },
      "requested_teams": {
        "type": "array"
      },
      "labels": {
        "type": "array"
      },
      "milestone": {
        "type": [
          "string",
          null
        ]
      },
      "draft": {
        "type": "boolean"
      },
      "commits_url": {
        "type": "string"
      },
      "review_comments_url": {
        "type": "string"
      },
      "review_comment_url": {
        "type": "string"
      },
      "comments_url": {
        "type": "string"
      },
      "statuses_url": {
        "type": "string"
      },
      "head": {
        "type": "object"
      },
      "base": {
        "type": "object"
      },
      "_links": {
        "type": "object"
      },
      "author_association": {
        "type": "string"
      },
      "auto_merge": {
        "type": [
          "Record<string, any>",
          null
        ]
      },
      "active_lock_reason": {
        "type": [
          "string",
          null
        ]
      },
      "merged": {
        "type": "boolean"
      },
      "mergeable": {
        "type": [
          "boolean",
          null
        ]
      },
      "rebaseable": {
        "type": [
          "boolean",
          null
        ]
      },
      "mergeable_state": {
        "type": "string"
      },
      "merged_by": {
        "type": [
          "GithubIssueCreator",
          null
        ]
      },
      "comments": {
        "type": "number"
      },
      "review_comments": {
        "type": "number"
      },
      "maintainer_can_modify": {
        "type": "boolean"
      },
      "commits": {
        "type": "number"
      },
      "additions": {
        "type": "number"
      },
      "deletions": {
        "type": "number"
      },
      "changed_files": {
        "type": "number"
      }
    },
    "required": [
      "url",
      "id",
      "node_id",
      "html_url",
      "diff_url",
      "patch_url",
      "issue_url",
      "number",
      "state",
      "locked",
      "title",
      "user",
      "body",
      "created_at",
      "updated_at",
      "closed_at",
      "merged_at",
      "merge_commit_sha",
      "assignee",
      "assignees",
      "requested_reviewers",
      "requested_teams",
      "labels",
      "milestone",
      "draft",
      "commits_url",
      "review_comments_url",
      "review_comment_url",
      "comments_url",
      "statuses_url",
      "head",
      "base",
      "_links",
      "author_association",
      "auto_merge",
      "active_lock_reason",
      "merged",
      "mergeable",
      "rebaseable",
      "mergeable_state",
      "merged_by",
      "comments",
      "review_comments",
      "maintainer_can_modify",
      "commits",
      "additions",
      "deletions",
      "changed_files"
    ]
  },
  "GithubIssueAssignee": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "login": {
        "type": "string"
      },
      "avatar_url": {
        "type": "string"
      },
      "html_url": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "login",
      "avatar_url",
      "html_url"
    ]
  },
  "GithubTeamRef": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "name": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name"
    ]
  },
  "GithubPullRequestReview": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "node_id": {
        "type": "string"
      },
      "user": {
        "type": {
          "$ref": "GithubIssueCreator"
        }
      },
      "body": {
        "type": "string"
      },
      "state": {
        "type": "string"
      },
      "html_url": {
        "type": "string"
      },
      "pull_request_url": {
        "type": "string"
      },
      "submitted_at": {
        "type": "string"
      },
      "commit_id": {
        "type": "string"
      },
      "author_association": {
        "type": "string"
      },
      "_links": {
        "type": "object"
      }
    },
    "required": [
      "id",
      "node_id",
      "user",
      "body",
      "state",
      "html_url",
      "pull_request_url",
      "submitted_at",
      "commit_id",
      "author_association",
      "_links"
    ]
  },
  "GithubDeleteRepositoryOutput": {
    "type": "object",
    "properties": {
      "success": {
        "type": "boolean"
      },
      "message": {
        "type": "string"
      }
    },
    "required": [
      "success",
      "message"
    ]
  },
  "GithubPullRequestCommentList": {
    "type": "object",
    "properties": {
      "comments": {
        "type": "array"
      }
    },
    "required": [
      "comments"
    ]
  },
  "GithubPullRequestFileList": {
    "type": "object",
    "properties": {
      "files": {
        "type": "array"
      }
    },
    "required": [
      "files"
    ]
  },
  "GithubPullRequestFile": {
    "type": "object",
    "properties": {
      "sha": {
        "type": "string"
      },
      "filename": {
        "type": "string"
      },
      "status": {
        "type": "string"
      },
      "additions": {
        "type": "number"
      },
      "deletions": {
        "type": "number"
      },
      "changes": {
        "type": "number"
      },
      "blob_url": {
        "type": "string"
      },
      "raw_url": {
        "type": "string"
      },
      "contents_url": {
        "type": "string"
      },
      "patch": {
        "type": "string"
      }
    },
    "required": [
      "sha",
      "filename",
      "status",
      "additions",
      "deletions",
      "changes",
      "blob_url",
      "raw_url",
      "contents_url",
      "patch"
    ]
  },
  "GithubCombinedStatus": {
    "type": "object",
    "properties": {
      "state": {
        "type": "string"
      },
      "sha": {
        "type": "string"
      },
      "total_count": {
        "type": "number"
      },
      "statuses": {
        "type": "array"
      },
      "repository": {
        "type": {
          "$ref": "GithubRepositoryForGithubCombinedStatus"
        }
      },
      "commit_url": {
        "type": "string"
      }
    },
    "required": [
      "state",
      "sha",
      "total_count",
      "statuses",
      "repository",
      "commit_url"
    ]
  },
  "GithubStatus": {
    "type": "object",
    "properties": {
      "url": {
        "type": "string"
      },
      "id": {
        "type": "number"
      },
      "node_id": {
        "type": "string"
      },
      "state": {
        "type": "string"
      },
      "context": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "target_url": {
        "type": "string"
      },
      "created_at": {
        "type": "string"
      },
      "updated_at": {
        "type": "string"
      }
    },
    "required": [
      "url",
      "id",
      "node_id",
      "state",
      "context",
      "description",
      "target_url",
      "created_at",
      "updated_at"
    ]
  },
  "GithubRepositoryForGithubCombinedStatus": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "node_id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "full_name": {
        "type": "string"
      },
      "private": {
        "type": "boolean"
      },
      "owner": {
        "type": {
          "$ref": "GithubIssueCreator"
        }
      },
      "html_url": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "fork": {
        "type": "boolean"
      },
      "url": {
        "type": "string"
      },
      "forks_url": {
        "type": "string"
      },
      "keys_url": {
        "type": "string"
      },
      "collaborators_url": {
        "type": "string"
      },
      "teams_url": {
        "type": "string"
      },
      "hooks_url": {
        "type": "string"
      },
      "issue_events_url": {
        "type": "string"
      },
      "events_url": {
        "type": "string"
      },
      "assignees_url": {
        "type": "string"
      },
      "branches_url": {
        "type": "string"
      },
      "tags_url": {
        "type": "string"
      },
      "blobs_url": {
        "type": "string"
      },
      "git_tags_url": {
        "type": "string"
      },
      "git_refs_url": {
        "type": "string"
      },
      "trees_url": {
        "type": "string"
      },
      "statuses_url": {
        "type": "string"
      },
      "languages_url": {
        "type": "string"
      },
      "stargazers_url": {
        "type": "string"
      },
      "contributors_url": {
        "type": "string"
      },
      "subscribers_url": {
        "type": "string"
      },
      "subscription_url": {
        "type": "string"
      },
      "commits_url": {
        "type": "string"
      },
      "git_commits_url": {
        "type": "string"
      },
      "comments_url": {
        "type": "string"
      },
      "issue_comment_url": {
        "type": "string"
      },
      "contents_url": {
        "type": "string"
      },
      "compare_url": {
        "type": "string"
      },
      "merges_url": {
        "type": "string"
      },
      "archive_url": {
        "type": "string"
      },
      "downloads_url": {
        "type": "string"
      },
      "issues_url": {
        "type": "string"
      },
      "pulls_url": {
        "type": "string"
      },
      "milestones_url": {
        "type": "string"
      },
      "notifications_url": {
        "type": "string"
      },
      "labels_url": {
        "type": "string"
      },
      "releases_url": {
        "type": "string"
      },
      "deployments_url": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "node_id",
      "name",
      "full_name",
      "private",
      "owner",
      "html_url",
      "description",
      "fork",
      "url",
      "forks_url",
      "keys_url",
      "collaborators_url",
      "teams_url",
      "hooks_url",
      "issue_events_url",
      "events_url",
      "assignees_url",
      "branches_url",
      "tags_url",
      "blobs_url",
      "git_tags_url",
      "git_refs_url",
      "trees_url",
      "statuses_url",
      "languages_url",
      "stargazers_url",
      "contributors_url",
      "subscribers_url",
      "subscription_url",
      "commits_url",
      "git_commits_url",
      "comments_url",
      "issue_comment_url",
      "contents_url",
      "compare_url",
      "merges_url",
      "archive_url",
      "downloads_url",
      "issues_url",
      "pulls_url",
      "milestones_url",
      "notifications_url",
      "labels_url",
      "releases_url",
      "deployments_url"
    ]
  },
  "GithubBranchList": {
    "type": "object",
    "properties": {
      "branches": {
        "type": "array"
      }
    },
    "required": [
      "branches"
    ]
  },
  "GithubBranch": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string"
      },
      "commit": {
        "type": "object"
      },
      "protected": {
        "type": "boolean"
      },
      "protection": {
        "type": "object"
      },
      "protection_url": {
        "type": "string"
      }
    },
    "required": [
      "name",
      "commit",
      "protected"
    ]
  },
  "GithubIssueList": {
    "type": "object",
    "properties": {
      "issues": {
        "type": "array"
      }
    },
    "required": [
      "issues"
    ]
  },
  "GithubPullRequestList": {
    "type": "object",
    "properties": {
      "pull_requests": {
        "type": "array"
      }
    },
    "required": [
      "pull_requests"
    ]
  },
  "GithubRepositoryList": {
    "type": "object",
    "properties": {
      "note": {
        "type": [
          "string",
          null
        ]
      },
      "repositories": {
        "type": "array"
      }
    },
    "required": [
      "note",
      "repositories"
    ]
  },
  "GithubMergeResult": {
    "type": "object",
    "properties": {
      "sha": {
        "type": "string"
      },
      "merged": {
        "type": "boolean"
      },
      "message": {
        "type": "string"
      }
    },
    "required": [
      "sha",
      "merged",
      "message"
    ]
  },
  "GithubBranchUpdateResult": {
    "type": "object",
    "properties": {
      "message": {
        "type": "string"
      },
      "url": {
        "type": "string"
      }
    },
    "required": [
      "message",
      "url"
    ]
  },
  "GithubWriteFileActionResult": {
    "type": "object",
    "properties": {
      "url": {
        "type": "string"
      },
      "status": {
        "type": "string"
      },
      "sha": {
        "type": "string"
      }
    },
    "required": [
      "url",
      "status",
      "sha"
    ]
  },
  "SlackReactionOutput": {
    "type": "object",
    "properties": {
      "ok": {
        "type": "boolean"
      },
      "error": {
        "type": "string"
      }
    },
    "required": [
      "ok"
    ]
  },
  "SlackMessageList": {
    "type": "object",
    "properties": {
      "ok": {
        "type": "boolean"
      },
      "messages": {
        "type": "array"
      },
      "has_more": {
        "type": "boolean"
      },
      "pin_count": {
        "type": "number"
      },
      "channel_actions_ts": {
        "type": "string"
      },
      "channel_actions_count": {
        "type": "number"
      },
      "response_metadata": {
        "type": {
          "$ref": "SlackResponseMetadata"
        }
      },
      "error": {
        "type": "string"
      }
    },
    "required": [
      "ok",
      "messages"
    ]
  },
  "SlackMessage": {
    "type": "object",
    "properties": {
      "type": {
        "type": "string"
      },
      "subtype": {
        "type": "string"
      },
      "ts": {
        "type": "string"
      },
      "user": {
        "type": "string"
      },
      "text": {
        "type": "string"
      },
      "thread_ts": {
        "type": "string"
      },
      "reply_count": {
        "type": "number"
      },
      "blocks": {
        "type": "array"
      },
      "attachments": {
        "type": "array"
      },
      "files": {
        "type": "array"
      },
      "reactions": {
        "type": "array"
      },
      "parent_user_id": {
        "type": "string"
      },
      "edited": {
        "type": {
          "$ref": "SlackEdited"
        }
      },
      "bot_id": {
        "type": "string"
      },
      "icons": {
        "type": "object"
      },
      "team": {
        "type": "string"
      },
      "app_id": {
        "type": "string"
      },
      "client_msg_id": {
        "type": "string"
      }
    },
    "required": [
      "type",
      "ts",
      "text"
    ]
  },
  "SlackBlock": {
    "type": "object",
    "properties": {
      "type": {
        "type": "string"
      },
      "block_id": {
        "type": "string"
      },
      "text": {
        "type": "object"
      },
      "elements": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "fields": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "accessory": {
        "type": "object"
      }
    },
    "required": [
      "type"
    ]
  },
  "SlackAttachment": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "fallback": {
        "type": "string"
      },
      "color": {
        "type": "string"
      },
      "pretext": {
        "type": "string"
      },
      "author_name": {
        "type": "string"
      },
      "author_link": {
        "type": "string"
      },
      "author_icon": {
        "type": "string"
      },
      "title": {
        "type": "string"
      },
      "title_link": {
        "type": "string"
      },
      "text": {
        "type": "string"
      },
      "fields": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "image_url": {
        "type": "string"
      },
      "thumb_url": {
        "type": "string"
      },
      "footer": {
        "type": "string"
      },
      "footer_icon": {
        "type": "string"
      },
      "ts": {
        "type": "number"
      }
    },
    "required": []
  },
  "SlackFile": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "filetype": {
        "type": "string"
      },
      "url_private": {
        "type": "string"
      },
      "url_private_download": {
        "type": "string"
      },
      "mimetype": {
        "type": "string"
      },
      "size": {
        "type": "number"
      },
      "title": {
        "type": "string"
      },
      "created": {
        "type": "number"
      },
      "timestamp": {
        "type": "number"
      },
      "user": {
        "type": "string"
      },
      "editable": {
        "type": "boolean"
      },
      "mode": {
        "type": "string"
      },
      "is_external": {
        "type": "boolean"
      },
      "external_type": {
        "type": "string"
      },
      "permalink": {
        "type": "string"
      },
      "preview": {
        "type": "string"
      },
      "accessible": {
        "type": {
          "$ref": "UrlAccessibleFile"
        }
      }
    },
    "required": [
      "id"
    ]
  },
  "UrlAccessibleFile": {
    "type": "object",
    "properties": {
      "url": {
        "type": "string"
      },
      "authentication": {
        "type": {
          "$ref": "UrlAuthentication"
        }
      }
    },
    "required": [
      "url",
      "authentication"
    ]
  },
  "UrlAuthentication": {
    "type": "object",
    "properties": {
      "providerKey": {
        "type": "string"
      },
      "connectionId": {
        "type": "string"
      }
    },
    "required": [
      "providerKey",
      "connectionId"
    ]
  },
  "SlackReaction": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string"
      },
      "count": {
        "type": "number"
      },
      "users": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "name",
      "count",
      "users"
    ]
  },
  "SlackEdited": {
    "type": "object",
    "properties": {
      "user": {
        "type": "string"
      },
      "ts": {
        "type": "string"
      }
    },
    "required": [
      "user",
      "ts"
    ]
  },
  "SlackResponseMetadata": {
    "type": "object",
    "properties": {
      "next_cursor": {
        "type": "string"
      }
    },
    "required": []
  },
  "SlackPermalinkOutput": {
    "type": "object",
    "properties": {
      "ok": {
        "type": "boolean"
      },
      "permalink": {
        "type": "string"
      },
      "channel": {
        "type": "string"
      },
      "error": {
        "type": "string"
      }
    },
    "required": [
      "ok"
    ]
  },
  "SlackUserInfo": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "is_bot": {
        "type": "boolean"
      },
      "is_admin": {
        "type": "boolean"
      },
      "is_owner": {
        "type": "boolean"
      },
      "tz": {
        "type": "string"
      },
      "profile": {
        "type": {
          "$ref": "SlackUserProfile"
        }
      }
    },
    "required": [
      "id",
      "name",
      "is_bot"
    ]
  },
  "SlackUserProfile": {
    "type": "object",
    "properties": {
      "real_name": {
        "type": "string"
      },
      "display_name": {
        "type": "string"
      },
      "email": {
        "type": "string"
      },
      "image_original": {
        "type": "string"
      },
      "image_512": {
        "type": "string"
      }
    },
    "required": []
  },
  "SlackConversationsList": {
    "type": "object",
    "properties": {
      "ok": {
        "type": "boolean"
      },
      "channels": {
        "type": "array"
      },
      "response_metadata": {
        "type": {
          "$ref": "SlackResponseMetadata"
        }
      },
      "error": {
        "type": "string"
      }
    },
    "required": [
      "ok",
      "channels"
    ]
  },
  "SlackConversation": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "is_channel": {
        "type": "boolean"
      },
      "is_group": {
        "type": "boolean"
      },
      "is_im": {
        "type": "boolean"
      },
      "is_mpim": {
        "type": "boolean"
      },
      "is_private": {
        "type": "boolean"
      },
      "is_member": {
        "type": "boolean"
      },
      "user": {
        "type": "string"
      },
      "num_members": {
        "type": "number"
      }
    },
    "required": [
      "id"
    ]
  },
  "SlackSearchResultList": {
    "type": "object",
    "properties": {
      "ok": {
        "type": "boolean"
      },
      "query": {
        "type": "string"
      },
      "messages": {
        "type": "object"
      },
      "error": {
        "type": "string"
      }
    },
    "required": [
      "ok",
      "query",
      "messages"
    ]
  },
  "SlackSendMessageOutput": {
    "type": "object",
    "properties": {
      "ok": {
        "type": "boolean"
      },
      "ts": {
        "type": "string"
      },
      "channel": {
        "type": "string"
      },
      "message_text": {
        "type": "string"
      }
    },
    "required": [
      "ok",
      "ts",
      "channel"
    ]
  },
  "SlackUpdateMessageOutput": {
    "type": "object",
    "properties": {
      "ok": {
        "type": "boolean"
      },
      "channel": {
        "type": "string"
      },
      "ts": {
        "type": "string"
      },
      "text": {
        "type": "string"
      },
      "error": {
        "type": "string"
      }
    },
    "required": [
      "ok"
    ]
  },
  "GoogleCalendarEvent": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "kind": {
        "type": "string"
      },
      "etag": {
        "type": "string"
      },
      "status": {
        "type": "string"
      },
      "htmlLink": {
        "type": "string"
      },
      "created": {
        "type": "string"
      },
      "updated": {
        "type": "string"
      },
      "summary": {
        "type": "string",
        "description": "Event title / name."
      },
      "description": {
        "type": "string",
        "description": "Contains e.g. the agenda or specifics of the meeting. Can contain HTML."
      },
      "location": {
        "type": "string",
        "description": "Free form text."
      },
      "creator": {
        "type": "any"
      },
      "organizer": {
        "type": "any"
      },
      "start": {
        "type": "any",
        "description": "Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter."
      },
      "end": {
        "type": "any",
        "description": "Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter."
      },
      "iCalUID": {
        "type": "string"
      },
      "sequence": {
        "type": "number"
      },
      "eventType": {
        "type": "string"
      },
      "attendees": {
        "type": "array",
        "items": {
          "type": "any"
        },
        "description": "A list of attendee email addresses."
      },
      "recurrence": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "recurringEventId": {
        "type": "string"
      },
      "reminders": {
        "type": "any"
      },
      "hangoutLink": {
        "type": "string"
      },
      "conferenceData": {
        "type": "any"
      },
      "anyoneCanAddSelf": {
        "type": "boolean"
      },
      "guestsCanInviteOthers": {
        "type": "boolean"
      },
      "guestsCanSeeOtherGuests": {
        "type": "boolean"
      },
      "guestsCanModify": {
        "type": "boolean"
      },
      "privateCopy": {
        "type": "boolean"
      },
      "transparency": {
        "type": "string"
      },
      "visibility": {
        "type": "string"
      },
      "colorId": {
        "type": "string"
      },
      "attachments": {
        "type": "array",
        "items": {
          "type": "any"
        }
      }
    },
    "required": [
      "id",
      "kind",
      "etag",
      "status",
      "htmlLink",
      "created",
      "updated",
      "summary",
      "creator",
      "organizer",
      "start",
      "end",
      "iCalUID",
      "eventType",
      "reminders"
    ]
  },
  "GoogleCalendarEventDeleteOutput": {
    "type": "object",
    "properties": {
      "event": {
        "type": {
          "$ref": "GoogleCalendarEvent"
        }
      },
      "deletedAt": {
        "type": "string"
      }
    },
    "required": [
      "event",
      "deletedAt"
    ]
  },
  "GoogleCalendarList": {
    "type": "object",
    "properties": {
      "calendars": {
        "type": "array",
        "items": {
          "type": "any"
        }
      },
      "nextPageToken": {
        "type": "string"
      }
    },
    "required": [
      "calendars"
    ]
  },
  "GoogleCalendarEventList": {
    "type": "object",
    "properties": {
      "items": {
        "type": "array",
        "items": {
          "type": "any"
        }
      },
      "nextPageToken": {
        "type": "string"
      },
      "timeZone": {
        "type": "string",
        "description": "Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar."
      }
    },
    "required": [
      "items",
      "timeZone"
    ]
  },
  "GmailDraftOutput": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "threadId": {
        "type": [
          "string",
          null
        ]
      }
    },
    "required": [
      "id",
      "threadId"
    ]
  },
  "GmailReplyDraftOutput": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "threadId": {
        "type": [
          "string",
          null
        ]
      }
    },
    "required": [
      "id",
      "threadId"
    ]
  },
  "GmailDeleteMessageOutput": {
    "type": "object",
    "properties": {
      "success": {
        "type": "boolean"
      }
    },
    "required": [
      "success"
    ]
  },
  "GmailMessage": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "threadId": {
        "type": "string"
      },
      "labelIds": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "snippet": {
        "type": "string"
      },
      "payload": {
        "type": "object"
      },
      "sizeEstimate": {
        "type": "number"
      },
      "historyId": {
        "type": "string"
      },
      "internalDate": {
        "type": "string"
      },
      "headers": {
        "type": "array"
      },
      "body": {
        "type": "string"
      },
      "mimeType": {
        "type": "string"
      },
      "filename": {
        "type": "string"
      },
      "attachments": {
        "type": "array"
      }
    },
    "required": [
      "id",
      "threadId"
    ]
  },
  "GmailHeader": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string"
      },
      "value": {
        "type": "string"
      }
    },
    "required": [
      "name",
      "value"
    ]
  },
  "GmailAttachmentInfo": {
    "type": "object",
    "properties": {
      "filename": {
        "type": "string"
      },
      "mimeType": {
        "type": "string"
      },
      "size": {
        "type": "number"
      },
      "attachmentId": {
        "type": "string"
      }
    },
    "required": [
      "filename",
      "mimeType",
      "size"
    ]
  },
  "GmailMessageList": {
    "type": "object",
    "properties": {
      "messages": {
        "type": "array"
      },
      "nextPageToken": {
        "type": "string"
      }
    },
    "required": [
      "messages"
    ]
  },
  "GmailBasicMessageDetails": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "threadId": {
        "type": "string"
      },
      "labelIds": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "snippet": {
        "type": "string"
      },
      "subject": {
        "type": "string"
      },
      "date": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "threadId"
    ]
  },
  "GmailSendEmailOutput": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "threadId": {
        "type": "string"
      },
      "labelIds": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "id",
      "threadId"
    ]
  },
  "DropboxEntry": {
    "type": "object",
    "properties": {
      ".tag": {
        "type": "string"
      },
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "path_display": {
        "type": "string"
      },
      "path_lower": {
        "type": "string"
      },
      "client_modified": {
        "type": "string"
      },
      "server_modified": {
        "type": "string"
      },
      "rev": {
        "type": "string"
      },
      "size": {
        "type": "number"
      },
      "is_downloadable": {
        "type": "boolean"
      },
      "content_hash": {
        "type": "string"
      }
    },
    "required": [
      ".tag",
      "id",
      "name",
      "path_display",
      "path_lower"
    ]
  },
  "DropboxFolder": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "path_display": {
        "type": "string"
      },
      "path_lower": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "path_display",
      "path_lower"
    ]
  },
  "DropboxDeleteResult": {
    "type": "object",
    "properties": {
      "metadata": {
        "type": {
          "$ref": "DropboxEntry"
        }
      }
    },
    "required": [
      "metadata"
    ]
  },
  "Anonymous_dropbox_action_fetchfile_output": {
    "type": "object",
    "properties": {
      "output": {
        "type": "string"
      }
    },
    "required": [
      "output"
    ]
  },
  "DropboxFile": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "path_display": {
        "type": "string"
      },
      "path_lower": {
        "type": "string"
      },
      "size": {
        "type": "number"
      },
      "content_hash": {
        "type": "string"
      },
      "server_modified": {
        "type": "string"
      },
      "content": {
        "type": "string"
      },
      "download_url": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "path_display",
      "path_lower",
      "size",
      "server_modified"
    ]
  },
  "DropboxFileList": {
    "type": "object",
    "properties": {
      "entries": {
        "type": "array"
      },
      "cursor": {
        "type": "string"
      },
      "has_more": {
        "type": "boolean"
      }
    },
    "required": [
      "entries",
      "has_more"
    ]
  },
  "DropboxSearchResult": {
    "type": "object",
    "properties": {
      "matches": {
        "type": "array"
      },
      "more": {
        "type": "boolean"
      },
      "start": {
        "type": "number"
      }
    },
    "required": [
      "matches",
      "more",
      "start"
    ]
  },
  "DropboxSearchMatch": {
    "type": "object",
    "properties": {
      "metadata": {
        "type": {
          "$ref": "DropboxEntry"
        }
      },
      "match_type": {
        "type": "string"
      }
    },
    "required": [
      "metadata",
      "match_type"
    ]
  },
  "NotionDatabase": {
    "type": "object",
    "properties": {
      "object": {
        "type": "string"
      },
      "id": {
        "type": "string"
      },
      "created_time": {
        "type": "string"
      },
      "last_edited_time": {
        "type": "string"
      },
      "created_by": {
        "type": {
          "$ref": "NotionUserReference"
        }
      },
      "last_edited_by": {
        "type": {
          "$ref": "NotionUserReference"
        }
      },
      "icon": {
        "type": [
          "NotionIcon",
          null
        ]
      },
      "cover": {
        "type": [
          "NotionCover",
          null
        ]
      },
      "parent": {
        "type": {
          "$ref": "NotionParentReference"
        }
      },
      "archived": {
        "type": "boolean"
      },
      "in_trash": {
        "type": "boolean"
      },
      "properties": {
        "type": {
          "$ref": "NotionProperties"
        }
      },
      "url": {
        "type": "string"
      },
      "public_url": {
        "type": [
          "string",
          null
        ]
      },
      "title": {
        "type": "array"
      },
      "description": {
        "type": "array"
      },
      "is_inline": {
        "type": "boolean"
      },
      "request_id": {
        "type": "string"
      }
    },
    "required": [
      "object",
      "id",
      "properties"
    ]
  },
  "NotionUserReference": {
    "type": "object",
    "properties": {
      "object": {
        "type": "string"
      },
      "id": {
        "type": "string"
      }
    },
    "required": [
      "object",
      "id"
    ]
  },
  "NotionParentReference": {
    "type": "object",
    "properties": {
      "type": {
        "type": "string"
      },
      "workspace": {
        "type": "boolean"
      },
      "page_id": {
        "type": "string"
      },
      "database_id": {
        "type": "string"
      }
    },
    "required": [
      "type"
    ]
  },
  "NotionProperties": {
    "type": "object",
    "properties": {
      "title": {
        "type": {
          "$ref": "NotionTitleProperty"
        }
      }
    },
    "required": []
  },
  "NotionTitleProperty": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "type": {
        "type": "string"
      },
      "title": {
        "type": "array"
      }
    },
    "required": []
  },
  "NotionRichText": {
    "type": "object",
    "properties": {
      "type": {
        "type": "string"
      },
      "text": {
        "type": {
          "$ref": "NotionRichTextContent"
        }
      },
      "annotations": {
        "type": {
          "$ref": "NotionRichTextAnnotations"
        }
      },
      "plain_text": {
        "type": "string"
      },
      "href": {
        "type": [
          "string",
          null
        ]
      }
    },
    "required": []
  },
  "NotionRichTextContent": {
    "type": "object",
    "properties": {
      "content": {
        "type": "string"
      },
      "link": {
        "type": [
          "Record<string, any>",
          null
        ]
      }
    },
    "required": []
  },
  "NotionRichTextAnnotations": {
    "type": "object",
    "properties": {
      "bold": {
        "type": "boolean"
      },
      "italic": {
        "type": "boolean"
      },
      "strikethrough": {
        "type": "boolean"
      },
      "underline": {
        "type": "boolean"
      },
      "code": {
        "type": "boolean"
      },
      "color": {
        "type": "string"
      }
    },
    "required": []
  },
  "NotionPageOrDatabase": {
    "type": "object",
    "properties": {
      "object": {
        "type": "string"
      },
      "id": {
        "type": "string"
      },
      "created_time": {
        "type": "string"
      },
      "last_edited_time": {
        "type": "string"
      },
      "created_by": {
        "type": {
          "$ref": "NotionUserReference"
        }
      },
      "last_edited_by": {
        "type": {
          "$ref": "NotionUserReference"
        }
      },
      "cover": {
        "type": [
          "NotionCover",
          null
        ]
      },
      "icon": {
        "type": [
          "NotionIcon",
          null
        ]
      },
      "parent": {
        "type": {
          "$ref": "NotionParentReference"
        }
      },
      "archived": {
        "type": "boolean"
      },
      "in_trash": {
        "type": "boolean"
      },
      "properties": {
        "type": {
          "$ref": "NotionProperties"
        }
      },
      "url": {
        "type": "string"
      },
      "public_url": {
        "type": [
          "string",
          null
        ]
      },
      "title": {
        "type": "array"
      },
      "description": {
        "type": "array"
      },
      "is_inline": {
        "type": "boolean"
      },
      "request_id": {
        "type": "string"
      }
    },
    "required": [
      "object",
      "id"
    ]
  },
  "NotionQueryDatabaseOutput": {
    "type": "object",
    "properties": {
      "object": {
        "type": "string"
      },
      "results": {
        "type": "array"
      },
      "next_cursor": {
        "type": [
          "string",
          null
        ]
      },
      "has_more": {
        "type": "boolean"
      },
      "type": {
        "type": "string"
      },
      "page": {
        "type": {
          "$ref": "NotionGenericObjectPlaceholder"
        }
      },
      "request_id": {
        "type": "string"
      }
    },
    "required": [
      "object",
      "results",
      "has_more"
    ]
  },
  "NotionGenericObjectPlaceholder": {
    "type": "object",
    "properties": {
      "_placeholder": {
        "type": "string"
      }
    },
    "required": []
  },
  "NotionSearchOutput": {
    "type": "object",
    "properties": {
      "object": {
        "type": "string"
      },
      "results": {
        "type": "array"
      },
      "next_cursor": {
        "type": [
          "string",
          null
        ]
      },
      "has_more": {
        "type": "boolean"
      },
      "type": {
        "type": "string"
      },
      "page_or_database": {
        "type": {
          "$ref": "NotionGenericObjectPlaceholder"
        }
      },
      "request_id": {
        "type": "string"
      }
    },
    "required": [
      "object",
      "results",
      "has_more"
    ]
  },
  "GoogleDocsDocument": {
    "type": "object",
    "properties": {
      "documentId": {
        "type": "string"
      },
      "title": {
        "type": "string"
      },
      "body": {
        "type": "object"
      },
      "headers": {
        "type": "object"
      },
      "footers": {
        "type": "object"
      },
      "footnotes": {
        "type": "object"
      },
      "documentStyle": {
        "type": "object"
      },
      "namedStyles": {
        "type": "object"
      },
      "revisionId": {
        "type": "string"
      },
      "suggestionsViewMode": {
        "type": "string"
      },
      "inlineObjects": {
        "type": "object"
      },
      "positionedObjects": {
        "type": "object"
      },
      "tabs": {
        "type": "array",
        "items": {
          "type": "object"
        }
      }
    },
    "required": []
  },
  "GoogleDocsUpdateDocumentOutput": {
    "type": "object",
    "properties": {
      "documentId": {
        "type": "string"
      },
      "replies": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "writeControl": {
        "type": "object"
      }
    },
    "required": [
      "documentId"
    ]
  },
  "LinearIssue": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "title": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "number": {
        "type": "number"
      },
      "priority": {
        "type": "number"
      },
      "url": {
        "type": "string"
      },
      "createdAt": {
        "type": "string"
      },
      "updatedAt": {
        "type": "string"
      },
      "state": {
        "type": {
          "$ref": "LinearState"
        }
      },
      "assignee": {
        "type": {
          "$ref": "LinearUser"
        }
      },
      "team": {
        "type": {
          "$ref": "LinearTeamBasic"
        }
      },
      "project": {
        "type": {
          "$ref": "LinearProjectBasic"
        }
      },
      "labels": {
        "type": "array"
      }
    },
    "required": [
      "id",
      "title",
      "number",
      "priority",
      "url",
      "createdAt",
      "updatedAt",
      "state",
      "team"
    ]
  },
  "LinearState": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "color": {
        "type": "string"
      },
      "type": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "color",
      "type"
    ]
  },
  "LinearUser": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "email": {
        "type": "string"
      },
      "displayName": {
        "type": "string"
      },
      "avatarUrl": {
        "type": [
          "string",
          null
        ]
      }
    },
    "required": [
      "id",
      "name",
      "email"
    ]
  },
  "LinearTeamBasic": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "key": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "key"
    ]
  },
  "LinearProjectBasic": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "icon": {
        "type": "string"
      },
      "color": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name"
    ]
  },
  "LinearLabel": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "color": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "color"
    ]
  },
  "LinearProject": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "url": {
        "type": "string"
      },
      "color": {
        "type": "string"
      },
      "state": {
        "type": "string"
      },
      "lead": {
        "type": {
          "$ref": "LinearUserBasic"
        }
      },
      "teams": {
        "type": "array"
      },
      "createdAt": {
        "type": "string"
      },
      "updatedAt": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "state",
      "teams",
      "createdAt",
      "updatedAt"
    ]
  },
  "LinearUserBasic": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "email": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name"
    ]
  },
  "LinearDeleteIssueOutput": {
    "type": "object",
    "properties": {
      "success": {
        "type": "boolean"
      },
      "issueId": {
        "type": "string"
      }
    },
    "required": [
      "success",
      "issueId"
    ]
  },
  "ModelResponse": {
    "type": "object",
    "properties": {
      "models": {
        "type": "array"
      }
    },
    "required": [
      "models"
    ]
  },
  "Model": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string"
      }
    },
    "required": [
      "name"
    ]
  },
  "LinearTeam": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "key": {
        "type": "string"
      },
      "description": {
        "type": [
          "string",
          null
        ]
      },
      "color": {
        "type": "string"
      },
      "private": {
        "type": "boolean"
      },
      "createdAt": {
        "type": "string"
      },
      "updatedAt": {
        "type": "string"
      },
      "members": {
        "type": "array"
      }
    },
    "required": [
      "id",
      "name",
      "key",
      "createdAt",
      "updatedAt",
      "members"
    ]
  },
  "LinearIssueList": {
    "type": "object",
    "properties": {
      "issues": {
        "type": "array"
      },
      "pageInfo": {
        "type": {
          "$ref": "PageInfo"
        }
      }
    },
    "required": [
      "issues"
    ]
  },
  "PageInfo": {
    "type": "object",
    "properties": {
      "hasNextPage": {
        "type": "boolean"
      },
      "endCursor": {
        "type": "string"
      }
    },
    "required": [
      "hasNextPage"
    ]
  },
  "LinearProjectList": {
    "type": "object",
    "properties": {
      "projects": {
        "type": "array"
      },
      "pageInfo": {
        "type": {
          "$ref": "PageInfo"
        }
      }
    },
    "required": [
      "projects"
    ]
  },
  "LinearTeamList": {
    "type": "object",
    "properties": {
      "teams": {
        "type": "array"
      },
      "pageInfo": {
        "type": {
          "$ref": "PageInfo"
        }
      }
    },
    "required": [
      "teams"
    ]
  },
  "GoogleSheetCreateOutput": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "url": {
        "type": "string"
      },
      "title": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "url",
      "title"
    ]
  },
  "Spreadsheet": {
    "type": "object",
    "properties": {
      "spreadsheetId": {
        "type": "string"
      },
      "properties": {
        "type": "object"
      },
      "sheets": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "namedRanges": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "spreadsheetUrl": {
        "type": "string"
      },
      "developerMetadata": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "dataSources": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "dataSourceSchedules": {
        "type": "array",
        "items": {
          "type": "object"
        }
      }
    },
    "required": [
      "spreadsheetId",
      "properties",
      "sheets",
      "namedRanges",
      "spreadsheetUrl",
      "developerMetadata",
      "dataSources",
      "dataSourceSchedules"
    ]
  },
  "GoogleSheetUpdateOutput": {
    "type": "object",
    "properties": {
      "spreadsheetId": {
        "type": "string"
      },
      "updatedRange": {
        "type": "string"
      },
      "updatedRows": {
        "type": "number"
      },
      "updatedColumns": {
        "type": "number"
      },
      "updatedCells": {
        "type": "number"
      }
    },
    "required": [
      "spreadsheetId",
      "updatedRange",
      "updatedRows",
      "updatedColumns",
      "updatedCells"
    ]
  },
  "Anonymous_googledrive_action_fetchdocument_output": {
    "type": "object",
    "properties": {
      "output": {
        "type": "string"
      }
    },
    "required": [
      "output"
    ]
  },
  "JSONDocument": {
    "type": "object",
    "properties": {
      "documentId": {
        "type": "string"
      },
      "title": {
        "type": "string"
      },
      "url": {
        "type": "string"
      },
      "tabs": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "revisionId": {
        "type": "string"
      },
      "suggestionsViewMode": {
        "type": [
          "DEFAULT_FOR_CURRENT_ACCESS",
          "SUGGESTIONS_INLINE",
          "PREVIEW_SUGGESTIONS_ACCEPTED",
          "PREVIEW_WITHOUT_SUGGESTIONS"
        ]
      },
      "body": {
        "type": "object"
      },
      "headers": {
        "type": "object"
      },
      "footers": {
        "type": "object"
      },
      "footnotes": {
        "type": "object"
      },
      "documentStyle": {
        "type": "object"
      },
      "suggestedDocumentStyleChanges": {
        "type": "object"
      },
      "namedStyles": {
        "type": "object"
      },
      "suggestedNamedStylesChanges": {
        "type": "object"
      },
      "lists": {
        "type": "object"
      },
      "namedRanges": {
        "type": "object"
      },
      "inlineObjects": {
        "type": "object"
      },
      "positionedObjects": {
        "type": "object"
      }
    },
    "required": [
      "documentId",
      "title",
      "url",
      "tabs",
      "revisionId",
      "suggestionsViewMode",
      "body",
      "headers",
      "footers",
      "footnotes",
      "documentStyle",
      "suggestedDocumentStyleChanges",
      "namedStyles",
      "suggestedNamedStylesChanges",
      "lists",
      "namedRanges",
      "inlineObjects",
      "positionedObjects"
    ]
  },
  "JSONSpreadsheet": {
    "type": "object",
    "properties": {
      "spreadsheetId": {
        "type": "string"
      },
      "properties": {
        "type": "object"
      },
      "sheets": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "namedRanges": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "spreadsheetUrl": {
        "type": "string"
      },
      "developerMetadata": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "dataSources": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "dataSourceSchedules": {
        "type": "array",
        "items": {
          "type": "object"
        }
      }
    },
    "required": [
      "spreadsheetId",
      "properties",
      "sheets",
      "namedRanges",
      "spreadsheetUrl",
      "developerMetadata",
      "dataSources",
      "dataSourceSchedules"
    ]
  },
  "FolderContent": {
    "type": "object",
    "properties": {
      "files": {
        "type": "array"
      },
      "folders": {
        "type": "array"
      },
      "cursor": {
        "type": "string"
      }
    },
    "required": [
      "files",
      "folders"
    ]
  },
  "GoogleDocument": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "mimeType": {
        "type": "string"
      },
      "parents": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "modifiedTime": {
        "type": "string"
      },
      "createdTime": {
        "type": "string"
      },
      "webViewLink": {
        "type": "string"
      },
      "kind": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "mimeType"
    ]
  },
  "GoogleDriveDocumentList": {
    "type": "object",
    "properties": {
      "documents": {
        "type": "array"
      },
      "nextPageToken": {
        "type": "string"
      }
    },
    "required": [
      "documents"
    ]
  },
  "GoogleDriveDocument": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "mimeType": {
        "type": "string"
      },
      "webViewLink": {
        "type": "string"
      },
      "modifiedTime": {
        "type": "string"
      },
      "createdTime": {
        "type": "string"
      },
      "parents": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "size": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "mimeType",
      "webViewLink",
      "modifiedTime",
      "createdTime",
      "parents",
      "size"
    ]
  },
  "GoogleDriveFolderList": {
    "type": "object",
    "properties": {
      "folders": {
        "type": "array"
      },
      "nextPageToken": {
        "type": "string"
      }
    },
    "required": [
      "folders"
    ]
  },
  "GoogleDriveFolder": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "mimeType": {
        "type": "string"
      },
      "webViewLink": {
        "type": "string"
      },
      "modifiedTime": {
        "type": "string"
      },
      "createdTime": {
        "type": "string"
      },
      "parents": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "id",
      "name",
      "mimeType",
      "webViewLink",
      "modifiedTime",
      "createdTime",
      "parents"
    ]
  },
  "LinkedInUserProfile": {
    "type": "object",
    "properties": {
      "sub": {
        "type": "string"
      },
      "email_verified": {
        "type": "boolean"
      },
      "name": {
        "type": "string"
      },
      "locale": {
        "type": "object"
      },
      "given_name": {
        "type": "string"
      },
      "family_name": {
        "type": "string"
      },
      "email": {
        "type": "string"
      },
      "picture": {
        "type": "string"
      }
    },
    "required": [
      "sub",
      "email_verified",
      "name",
      "locale",
      "given_name",
      "family_name",
      "email",
      "picture"
    ]
  },
  "LinkedInPostOutput": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      }
    },
    "required": [
      "id"
    ]
  },
  "XSocialUserProfile": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "username": {
        "type": "string"
      },
      "profile_image_url": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "location": {
        "type": "string"
      },
      "url": {
        "type": "string"
      },
      "protected": {
        "type": "boolean"
      },
      "verified": {
        "type": "boolean"
      },
      "followers_count": {
        "type": "number"
      },
      "following_count": {
        "type": "number"
      },
      "tweet_count": {
        "type": "number"
      },
      "listed_count": {
        "type": "number"
      }
    },
    "required": [
      "id",
      "name",
      "username",
      "profile_image_url",
      "description",
      "location",
      "url",
      "protected",
      "verified",
      "followers_count",
      "following_count",
      "tweet_count",
      "listed_count"
    ]
  },
  "XSocialPostOutput": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "text": {
        "type": "string"
      },
      "created_at": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "text",
      "created_at"
    ]
  }
};

export const SYNC_OUTPUT_MODELS_JSON_SCHEMA = {
  "SlackSyncMessage": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "channel_id": {
        "type": "string"
      },
      "message": {
        "type": {
          "$ref": "SlackMessage"
        }
      }
    },
    "required": [
      "id",
      "channel_id",
      "message"
    ]
  },
  "SlackMessage": {
    "type": "object",
    "properties": {
      "type": {
        "type": "string"
      },
      "subtype": {
        "type": "string"
      },
      "ts": {
        "type": "string"
      },
      "user": {
        "type": "string"
      },
      "text": {
        "type": "string"
      },
      "thread_ts": {
        "type": "string"
      },
      "reply_count": {
        "type": "number"
      },
      "blocks": {
        "type": "array"
      },
      "attachments": {
        "type": "array"
      },
      "files": {
        "type": "array"
      },
      "reactions": {
        "type": "array"
      },
      "parent_user_id": {
        "type": "string"
      },
      "edited": {
        "type": {
          "$ref": "SlackEdited"
        }
      },
      "bot_id": {
        "type": "string"
      },
      "icons": {
        "type": "object"
      },
      "team": {
        "type": "string"
      },
      "app_id": {
        "type": "string"
      },
      "client_msg_id": {
        "type": "string"
      }
    },
    "required": [
      "type",
      "ts",
      "text"
    ]
  },
  "SlackBlock": {
    "type": "object",
    "properties": {
      "type": {
        "type": "string"
      },
      "block_id": {
        "type": "string"
      },
      "text": {
        "type": "object"
      },
      "elements": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "fields": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "accessory": {
        "type": "object"
      }
    },
    "required": [
      "type"
    ]
  },
  "SlackAttachment": {
    "type": "object",
    "properties": {
      "id": {
        "type": "number"
      },
      "fallback": {
        "type": "string"
      },
      "color": {
        "type": "string"
      },
      "pretext": {
        "type": "string"
      },
      "author_name": {
        "type": "string"
      },
      "author_link": {
        "type": "string"
      },
      "author_icon": {
        "type": "string"
      },
      "title": {
        "type": "string"
      },
      "title_link": {
        "type": "string"
      },
      "text": {
        "type": "string"
      },
      "fields": {
        "type": "array",
        "items": {
          "type": "object"
        }
      },
      "image_url": {
        "type": "string"
      },
      "thumb_url": {
        "type": "string"
      },
      "footer": {
        "type": "string"
      },
      "footer_icon": {
        "type": "string"
      },
      "ts": {
        "type": "number"
      }
    },
    "required": []
  },
  "SlackFile": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "filetype": {
        "type": "string"
      },
      "url_private": {
        "type": "string"
      },
      "url_private_download": {
        "type": "string"
      },
      "mimetype": {
        "type": "string"
      },
      "size": {
        "type": "number"
      },
      "title": {
        "type": "string"
      },
      "created": {
        "type": "number"
      },
      "timestamp": {
        "type": "number"
      },
      "user": {
        "type": "string"
      },
      "editable": {
        "type": "boolean"
      },
      "mode": {
        "type": "string"
      },
      "is_external": {
        "type": "boolean"
      },
      "external_type": {
        "type": "string"
      },
      "permalink": {
        "type": "string"
      },
      "preview": {
        "type": "string"
      },
      "accessible": {
        "type": {
          "$ref": "UrlAccessibleFile"
        }
      }
    },
    "required": [
      "id"
    ]
  },
  "UrlAccessibleFile": {
    "type": "object",
    "properties": {
      "url": {
        "type": "string"
      },
      "authentication": {
        "type": {
          "$ref": "UrlAuthentication"
        }
      }
    },
    "required": [
      "url",
      "authentication"
    ]
  },
  "UrlAuthentication": {
    "type": "object",
    "properties": {
      "providerKey": {
        "type": "string"
      },
      "connectionId": {
        "type": "string"
      }
    },
    "required": [
      "providerKey",
      "connectionId"
    ]
  },
  "SlackReaction": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string"
      },
      "count": {
        "type": "number"
      },
      "users": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    },
    "required": [
      "name",
      "count",
      "users"
    ]
  },
  "SlackEdited": {
    "type": "object",
    "properties": {
      "user": {
        "type": "string"
      },
      "ts": {
        "type": "string"
      }
    },
    "required": [
      "user",
      "ts"
    ]
  },
  "GoogleCalendarEvent": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "kind": {
        "type": "string"
      },
      "etag": {
        "type": "string"
      },
      "status": {
        "type": "string"
      },
      "htmlLink": {
        "type": "string"
      },
      "created": {
        "type": "string"
      },
      "updated": {
        "type": "string"
      },
      "summary": {
        "type": "string"
      },
      "description": {
        "type": "string"
      },
      "location": {
        "type": "string"
      },
      "creator": {
        "type": "any"
      },
      "organizer": {
        "type": "any"
      },
      "start": {
        "type": "any"
      },
      "end": {
        "type": "any"
      },
      "iCalUID": {
        "type": "string"
      },
      "sequence": {
        "type": "number"
      },
      "eventType": {
        "type": "string"
      },
      "attendees": {
        "type": "array",
        "items": {
          "type": "any"
        }
      },
      "recurrence": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "recurringEventId": {
        "type": "string"
      },
      "reminders": {
        "type": "any"
      },
      "hangoutLink": {
        "type": "string"
      },
      "conferenceData": {
        "type": "any"
      },
      "anyoneCanAddSelf": {
        "type": "boolean"
      },
      "guestsCanInviteOthers": {
        "type": "boolean"
      },
      "guestsCanSeeOtherGuests": {
        "type": "boolean"
      },
      "guestsCanModify": {
        "type": "boolean"
      },
      "privateCopy": {
        "type": "boolean"
      },
      "transparency": {
        "type": "string"
      },
      "visibility": {
        "type": "string"
      },
      "colorId": {
        "type": "string"
      },
      "attachments": {
        "type": "array",
        "items": {
          "type": "any"
        }
      }
    },
    "required": [
      "id",
      "kind",
      "etag",
      "status",
      "htmlLink",
      "created",
      "updated",
      "summary",
      "creator",
      "organizer",
      "start",
      "end",
      "iCalUID",
      "eventType",
      "reminders"
    ]
  },
  "GmailEmail": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "sender": {
        "type": "string"
      },
      "recipients": {
        "type": "string"
      },
      "date": {
        "type": "string"
      },
      "subject": {
        "type": "string"
      },
      "body": {
        "type": "string"
      },
      "attachments": {
        "type": "array"
      },
      "threadId": {
        "type": "string"
      },
      "isDraft": {
        "type": "boolean"
      },
      "labels": {
        "type": "array",
        "items": {
          "type": "string"
        }
      },
      "snippet": {
        "type": "string"
      },
      "cc": {
        "type": "string"
      },
      "bcc": {
        "type": "string"
      },
      "messageId": {
        "type": "string"
      },
      "inReplyTo": {
        "type": "string"
      },
      "references": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "sender",
      "recipients",
      "date",
      "subject",
      "body",
      "attachments",
      "threadId",
      "isDraft",
      "labels",
      "snippet",
      "cc",
      "bcc",
      "messageId",
      "inReplyTo",
      "references"
    ]
  },
  "Attachments": {
    "type": "object",
    "properties": {
      "filename": {
        "type": "string"
      },
      "mimeType": {
        "type": "string"
      },
      "size": {
        "type": "number"
      },
      "attachmentId": {
        "type": "string"
      }
    },
    "required": [
      "filename",
      "mimeType",
      "size",
      "attachmentId"
    ]
  },
  "DropboxFile": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "name": {
        "type": "string"
      },
      "path_display": {
        "type": "string"
      },
      "path_lower": {
        "type": "string"
      },
      "size": {
        "type": "number"
      },
      "content_hash": {
        "type": "string"
      },
      "server_modified": {
        "type": "string"
      },
      "content": {
        "type": "string"
      },
      "download_url": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "name",
      "path_display",
      "path_lower",
      "size",
      "server_modified"
    ]
  },
  "Document": {
    "type": "object",
    "properties": {
      "id": {
        "type": "string"
      },
      "url": {
        "type": "string"
      },
      "mimeType": {
        "type": "string"
      },
      "title": {
        "type": "string"
      },
      "updatedAt": {
        "type": "string"
      }
    },
    "required": [
      "id",
      "url",
      "mimeType",
      "title",
      "updatedAt"
    ]
  }
};

export const ACTION_INPUT_MODELS_KEYED = {
  "harvest:add-historical-time-entry": { model: "HarvestAddHistoricalTimeEntryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestAddHistoricalTimeEntryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestAddHistoricalTimeEntryInput"] },
  "harvest:create-client": { model: "HarvestCreateClientInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestCreateClientInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestCreateClientInput"] },
  "harvest:create-project": { model: "HarvestCreateProjectInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestCreateProjectInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestCreateProjectInput"] },
  "harvest:delete-project": { model: "HarvestProjectInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestProjectInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestProjectInput"] },
  "harvest:delete-time-entry": { model: "HarvestTimeEntryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestTimeEntryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestTimeEntryInput"] },
  "harvest:get-client": { model: "HarvestClientInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestClientInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestClientInput"] },
  "harvest:get-project": { model: "HarvestProjectInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestProjectInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestProjectInput"] },
  "harvest:get-time-entry": { model: "HarvestTimeEntryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestTimeEntryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestTimeEntryInput"] },
  "harvest:list-projects": { model: "HarvestProjectsInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestProjectsInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestProjectsInput"] },
  "harvest:list-project-tasks": { model: "HarvestProjectTasksInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestProjectTasksInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestProjectTasksInput"] },
  "harvest:list-tasks": { model: "HarvestTasksInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestTasksInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestTasksInput"] },
  "harvest:list-time-entries": { model: "HarvestTimeEntriesInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestTimeEntriesInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestTimeEntriesInput"] },
  "harvest:restart-timer": { model: "HarvestTimeEntryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestTimeEntryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestTimeEntryInput"] },
  "harvest:start-timer": { model: "HarvestStartTimerInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestStartTimerInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestStartTimerInput"] },
  "harvest:stop-timer": { model: "HarvestTimeEntryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestTimeEntryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestTimeEntryInput"] },
  "harvest:update-time-entry": { model: "HarvestUpdateTimeEntryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["HarvestUpdateTimeEntryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["HarvestUpdateTimeEntryInput"] },
  "github:add-pull-request-review-comment": { model: "GithubAddPullRequestReviewCommentInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubAddPullRequestReviewCommentInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubAddPullRequestReviewCommentInput"] },
  "github:create-issue": { model: "GithubCreateIssueInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubCreateIssueInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubCreateIssueInput"] },
  "github:create-organization-repository": { model: "GithubCreateOrganizationRepositoryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubCreateOrganizationRepositoryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubCreateOrganizationRepositoryInput"] },
  "github:create-pull-request": { model: "GithubCreatePullRequestInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubCreatePullRequestInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubCreatePullRequestInput"] },
  "github:create-pull-request-review": { model: "GithubCreatePullRequestReviewInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubCreatePullRequestReviewInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubCreatePullRequestReviewInput"] },
  "github:create-repository": { model: "GithubCreateRepositoryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubCreateRepositoryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubCreateRepositoryInput"] },
  "github:delete-repository": { model: "GithubRepositoryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubRepositoryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubRepositoryInput"] },
  "github:get-issue": { model: "GithubIssueInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubIssueInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubIssueInput"] },
  "github:get-pull-request": { model: "GithubPullRequestInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubPullRequestInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubPullRequestInput"] },
  "github:get-pull-request-comments": { model: "GithubPullRequestInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubPullRequestInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubPullRequestInput"] },
  "github:get-pull-request-files": { model: "GithubPullRequestInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubPullRequestInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubPullRequestInput"] },
  "github:get-pull-request-status": { model: "GithubPullRequestInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubPullRequestInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubPullRequestInput"] },
  "github:get-repository": { model: "GithubRepositoryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubRepositoryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubRepositoryInput"] },
  "github:list-branches": { model: "GithubListBranchesInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubListBranchesInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubListBranchesInput"] },
  "github:list-issues": { model: "GithubIssuesInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubIssuesInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubIssuesInput"] },
  "github:list-pull-requests": { model: "GithubListPullRequestsInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubListPullRequestsInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubListPullRequestsInput"] },
  "github:merge-pull-request": { model: "GithubMergePullRequestInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubMergePullRequestInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubMergePullRequestInput"] },
  "github:update-issue": { model: "GithubUpdateIssueInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubUpdateIssueInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubUpdateIssueInput"] },
  "github:update-pull-request": { model: "GithubUpdatePullRequestInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubUpdatePullRequestInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubUpdatePullRequestInput"] },
  "github:update-pull-request-branch": { model: "GithubUpdatePullRequestBranchInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubUpdatePullRequestBranchInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubUpdatePullRequestBranchInput"] },
  "github:update-repository": { model: "GithubUpdateRepositoryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubUpdateRepositoryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubUpdateRepositoryInput"] },
  "github:write-file": { model: "GithubWriteFileInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GithubWriteFileInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GithubWriteFileInput"] },
  "slack:add-reaction-as-user": { model: "SlackAddReactionInput", zodSchema: ACTION_INPUT_MODELS_ZOD["SlackAddReactionInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["SlackAddReactionInput"] },
  "slack:get-channel-history": { model: "SlackGetChannelHistoryInput", zodSchema: ACTION_INPUT_MODELS_ZOD["SlackGetChannelHistoryInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["SlackGetChannelHistoryInput"] },
  "slack:get-message-permalink": { model: "SlackGetPermalinkInput", zodSchema: ACTION_INPUT_MODELS_ZOD["SlackGetPermalinkInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["SlackGetPermalinkInput"] },
  "slack:get-user-info": { model: "SlackGetUserInfoInput", zodSchema: ACTION_INPUT_MODELS_ZOD["SlackGetUserInfoInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["SlackGetUserInfoInput"] },
  "slack:list-channels": { model: "SlackListChannelsInput", zodSchema: ACTION_INPUT_MODELS_ZOD["SlackListChannelsInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["SlackListChannelsInput"] },
  "slack:search-messages": { model: "SlackSearchMessagesInput", zodSchema: ACTION_INPUT_MODELS_ZOD["SlackSearchMessagesInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["SlackSearchMessagesInput"] },
  "slack:send-message-as-user": { model: "SlackSendMessageInput", zodSchema: ACTION_INPUT_MODELS_ZOD["SlackSendMessageInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["SlackSendMessageInput"] },
  "slack:update-message-as-user": { model: "SlackUpdateMessageInput", zodSchema: ACTION_INPUT_MODELS_ZOD["SlackUpdateMessageInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["SlackUpdateMessageInput"] },
  "google-calendar:create-event": { model: "GoogleCalendarEventInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GoogleCalendarEventInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GoogleCalendarEventInput"] },
  "google-calendar:delete-event": { model: "GoogleCalendarEventDeleteInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GoogleCalendarEventDeleteInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GoogleCalendarEventDeleteInput"] },
  "google-calendar:list-events": { model: "GoogleCalendarEventsInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GoogleCalendarEventsInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GoogleCalendarEventsInput"] },
  "google-calendar:update-event": { model: "GoogleCalendarEventUpdateInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GoogleCalendarEventUpdateInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GoogleCalendarEventUpdateInput"] },
  "google-mail:compose-draft": { model: "GmailDraftInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GmailDraftInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GmailDraftInput"] },
  "google-mail:compose-draft-reply": { model: "GmailReplyDraftInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GmailReplyDraftInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GmailReplyDraftInput"] },
  "google-mail:delete-message": { model: "GmailMessageIdInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GmailMessageIdInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GmailMessageIdInput"] },
  "google-mail:get-message": { model: "GmailGetMessageInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GmailGetMessageInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GmailGetMessageInput"] },
  "google-mail:list-messages": { model: "GmailListMessagesInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GmailListMessagesInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GmailListMessagesInput"] },
  "google-mail:modify-message-labels": { model: "GmailModifyMessageLabelsInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GmailModifyMessageLabelsInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GmailModifyMessageLabelsInput"] },
  "google-mail:send-email": { model: "GmailSendEmailInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GmailSendEmailInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GmailSendEmailInput"] },
  "google-mail:trash-message": { model: "GmailMessageIdInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GmailMessageIdInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GmailMessageIdInput"] },
  "google-mail:untrash-message": { model: "GmailMessageIdInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GmailMessageIdInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GmailMessageIdInput"] },
  "dropbox:copy-file": { model: "DropboxCopyInput", zodSchema: ACTION_INPUT_MODELS_ZOD["DropboxCopyInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["DropboxCopyInput"] },
  "dropbox:create-folder": { model: "DropboxCreateFolderInput", zodSchema: ACTION_INPUT_MODELS_ZOD["DropboxCreateFolderInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["DropboxCreateFolderInput"] },
  "dropbox:delete-file": { model: "DropboxDeleteInput", zodSchema: ACTION_INPUT_MODELS_ZOD["DropboxDeleteInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["DropboxDeleteInput"] },
  "dropbox:fetch-file": { model: "Anonymous_dropbox_action_fetchfile_input", zodSchema: ACTION_INPUT_MODELS_ZOD["Anonymous_dropbox_action_fetchfile_input"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["Anonymous_dropbox_action_fetchfile_input"] },
  "dropbox:get-file": { model: "DropboxGetFileInput", zodSchema: ACTION_INPUT_MODELS_ZOD["DropboxGetFileInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["DropboxGetFileInput"] },
  "dropbox:list-files": { model: "DropboxListFilesInput", zodSchema: ACTION_INPUT_MODELS_ZOD["DropboxListFilesInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["DropboxListFilesInput"] },
  "dropbox:move-file": { model: "DropboxMoveInput", zodSchema: ACTION_INPUT_MODELS_ZOD["DropboxMoveInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["DropboxMoveInput"] },
  "dropbox:search-files": { model: "DropboxSearchInput", zodSchema: ACTION_INPUT_MODELS_ZOD["DropboxSearchInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["DropboxSearchInput"] },
  "dropbox:upload-file": { model: "DropboxUploadFileInput", zodSchema: ACTION_INPUT_MODELS_ZOD["DropboxUploadFileInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["DropboxUploadFileInput"] },
  "notion:create-database": { model: "NotionCreateDatabaseInput", zodSchema: ACTION_INPUT_MODELS_ZOD["NotionCreateDatabaseInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["NotionCreateDatabaseInput"] },
  "notion:create-page": { model: "NotionCreatePageInput", zodSchema: ACTION_INPUT_MODELS_ZOD["NotionCreatePageInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["NotionCreatePageInput"] },
  "notion:get-database": { model: "NotionGetDatabaseInput", zodSchema: ACTION_INPUT_MODELS_ZOD["NotionGetDatabaseInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["NotionGetDatabaseInput"] },
  "notion:get-page": { model: "NotionGetPageInput", zodSchema: ACTION_INPUT_MODELS_ZOD["NotionGetPageInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["NotionGetPageInput"] },
  "notion:query-database": { model: "NotionQueryDatabaseInput", zodSchema: ACTION_INPUT_MODELS_ZOD["NotionQueryDatabaseInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["NotionQueryDatabaseInput"] },
  "notion:search": { model: "NotionSearchInput", zodSchema: ACTION_INPUT_MODELS_ZOD["NotionSearchInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["NotionSearchInput"] },
  "notion:update-database": { model: "NotionUpdateDatabaseInput", zodSchema: ACTION_INPUT_MODELS_ZOD["NotionUpdateDatabaseInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["NotionUpdateDatabaseInput"] },
  "notion:update-page": { model: "NotionUpdatePageInput", zodSchema: ACTION_INPUT_MODELS_ZOD["NotionUpdatePageInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["NotionUpdatePageInput"] },
  "google-docs:create-document": { model: "GoogleDocsCreateDocumentInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GoogleDocsCreateDocumentInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GoogleDocsCreateDocumentInput"] },
  "google-docs:get-document": { model: "GoogleDocsGetDocumentInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GoogleDocsGetDocumentInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GoogleDocsGetDocumentInput"] },
  "google-docs:update-document": { model: "GoogleDocsUpdateDocumentInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GoogleDocsUpdateDocumentInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GoogleDocsUpdateDocumentInput"] },
  "linear:create-issue": { model: "LinearCreateIssueInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinearCreateIssueInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinearCreateIssueInput"] },
  "linear:create-project": { model: "LinearCreateProjectInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinearCreateProjectInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinearCreateProjectInput"] },
  "linear:delete-issue": { model: "LinearIssueInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinearIssueInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinearIssueInput"] },
  "linear:get-issue": { model: "LinearIssueInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinearIssueInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinearIssueInput"] },
  "linear:get-project": { model: "LinearProjectInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinearProjectInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinearProjectInput"] },
  "linear:get-team": { model: "LinearTeamInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinearTeamInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinearTeamInput"] },
  "linear:list-issues": { model: "LinearIssuesInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinearIssuesInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinearIssuesInput"] },
  "linear:list-projects": { model: "LinearProjectsInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinearProjectsInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinearProjectsInput"] },
  "linear:list-teams": { model: "LinearTeamsInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinearTeamsInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinearTeamsInput"] },
  "linear:update-issue": { model: "LinearUpdateIssueInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinearUpdateIssueInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinearUpdateIssueInput"] },
  "linear:update-project": { model: "LinearUpdateProjectInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinearUpdateProjectInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinearUpdateProjectInput"] },
  "google-sheet:create-sheet": { model: "GoogleSheetCreateInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GoogleSheetCreateInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GoogleSheetCreateInput"] },
  "google-sheet:fetch-spreadsheet": { model: "SpreadsheetId", zodSchema: ACTION_INPUT_MODELS_ZOD["SpreadsheetId"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["SpreadsheetId"] },
  "google-sheet:update-sheet": { model: "GoogleSheetUpdateInput", zodSchema: ACTION_INPUT_MODELS_ZOD["GoogleSheetUpdateInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["GoogleSheetUpdateInput"] },
  "google-drive:fetch-document": { model: "IdEntity", zodSchema: ACTION_INPUT_MODELS_ZOD["IdEntity"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["IdEntity"] },
  "google-drive:fetch-google-doc": { model: "IdEntity", zodSchema: ACTION_INPUT_MODELS_ZOD["IdEntity"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["IdEntity"] },
  "google-drive:fetch-google-sheet": { model: "IdEntity", zodSchema: ACTION_INPUT_MODELS_ZOD["IdEntity"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["IdEntity"] },
  "google-drive:folder-content": { model: "FolderContentInput", zodSchema: ACTION_INPUT_MODELS_ZOD["FolderContentInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["FolderContentInput"] },
  "google-drive:list-documents": { model: "ListDocumentsInput", zodSchema: ACTION_INPUT_MODELS_ZOD["ListDocumentsInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["ListDocumentsInput"] },
  "google-drive:upload-document": { model: "UploadFileInput", zodSchema: ACTION_INPUT_MODELS_ZOD["UploadFileInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["UploadFileInput"] },
  "linkedin:send-post": { model: "LinkedInPostInput", zodSchema: ACTION_INPUT_MODELS_ZOD["LinkedInPostInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["LinkedInPostInput"] },
  "twitter-v2:send-post": { model: "XSocialPostInput", zodSchema: ACTION_INPUT_MODELS_ZOD["XSocialPostInput"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["XSocialPostInput"] },
};

export const ACTION_OUTPUT_MODELS_KEYED = {
  "harvest:add-historical-time-entry": { model: "HarvestTimeEntry", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestTimeEntry"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestTimeEntry"] },
  "harvest:create-client": { model: "HarvestClient", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestClient"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestClient"] },
  "harvest:create-project": { model: "HarvestProject", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestProject"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestProject"] },
  "harvest:delete-project": { model: "HarvestDeleteProjectOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestDeleteProjectOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestDeleteProjectOutput"] },
  "harvest:delete-time-entry": { model: "HarvestDeleteTimeEntryOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestDeleteTimeEntryOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestDeleteTimeEntryOutput"] },
  "harvest:get-client": { model: "HarvestClient", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestClient"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestClient"] },
  "harvest:get-project": { model: "HarvestProject", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestProject"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestProject"] },
  "harvest:get-time-entry": { model: "HarvestTimeEntry", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestTimeEntry"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestTimeEntry"] },
  "harvest:list-clients": { model: "HarvestClientList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestClientList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestClientList"] },
  "harvest:list-projects": { model: "HarvestProjectList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestProjectList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestProjectList"] },
  "harvest:list-project-tasks": { model: "HarvestProjectTaskList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestProjectTaskList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestProjectTaskList"] },
  "harvest:list-tasks": { model: "HarvestTaskList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestTaskList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestTaskList"] },
  "harvest:list-time-entries": { model: "HarvestTimeEntryList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestTimeEntryList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestTimeEntryList"] },
  "harvest:restart-timer": { model: "HarvestTimeEntry", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestTimeEntry"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestTimeEntry"] },
  "harvest:start-timer": { model: "HarvestTimeEntry", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestTimeEntry"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestTimeEntry"] },
  "harvest:stop-timer": { model: "HarvestTimeEntry", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestTimeEntry"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestTimeEntry"] },
  "harvest:update-time-entry": { model: "HarvestTimeEntry", zodSchema: ACTION_OUTPUT_MODELS_ZOD["HarvestTimeEntry"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["HarvestTimeEntry"] },
  "github:add-pull-request-review-comment": { model: "GithubPullRequestComment", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubPullRequestComment"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubPullRequestComment"] },
  "github:create-issue": { model: "GithubIssue", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubIssue"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubIssue"] },
  "github:create-organization-repository": { model: "GithubRepository", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubRepository"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubRepository"] },
  "github:create-pull-request": { model: "GithubPullRequest", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubPullRequest"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubPullRequest"] },
  "github:create-pull-request-review": { model: "GithubPullRequestReview", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubPullRequestReview"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubPullRequestReview"] },
  "github:create-repository": { model: "GithubRepository", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubRepository"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubRepository"] },
  "github:delete-repository": { model: "GithubDeleteRepositoryOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubDeleteRepositoryOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubDeleteRepositoryOutput"] },
  "github:get-issue": { model: "GithubIssue", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubIssue"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubIssue"] },
  "github:get-pull-request": { model: "GithubPullRequest", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubPullRequest"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubPullRequest"] },
  "github:get-pull-request-comments": { model: "GithubPullRequestCommentList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubPullRequestCommentList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubPullRequestCommentList"] },
  "github:get-pull-request-files": { model: "GithubPullRequestFileList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubPullRequestFileList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubPullRequestFileList"] },
  "github:get-pull-request-status": { model: "GithubCombinedStatus", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubCombinedStatus"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubCombinedStatus"] },
  "github:get-repository": { model: "GithubRepository", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubRepository"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubRepository"] },
  "github:list-branches": { model: "GithubBranchList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubBranchList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubBranchList"] },
  "github:list-issues": { model: "GithubIssueList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubIssueList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubIssueList"] },
  "github:list-pull-requests": { model: "GithubPullRequestList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubPullRequestList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubPullRequestList"] },
  "github:list-repositories": { model: "GithubRepositoryList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubRepositoryList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubRepositoryList"] },
  "github:merge-pull-request": { model: "GithubMergeResult", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubMergeResult"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubMergeResult"] },
  "github:update-issue": { model: "GithubIssue", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubIssue"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubIssue"] },
  "github:update-pull-request": { model: "GithubPullRequest", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubPullRequest"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubPullRequest"] },
  "github:update-pull-request-branch": { model: "GithubBranchUpdateResult", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubBranchUpdateResult"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubBranchUpdateResult"] },
  "github:update-repository": { model: "GithubRepository", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubRepository"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubRepository"] },
  "github:write-file": { model: "GithubWriteFileActionResult", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GithubWriteFileActionResult"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GithubWriteFileActionResult"] },
  "slack:add-reaction-as-user": { model: "SlackReactionOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["SlackReactionOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["SlackReactionOutput"] },
  "slack:get-channel-history": { model: "SlackMessageList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["SlackMessageList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["SlackMessageList"] },
  "slack:get-message-permalink": { model: "SlackPermalinkOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["SlackPermalinkOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["SlackPermalinkOutput"] },
  "slack:get-user-info": { model: "SlackUserInfo", zodSchema: ACTION_OUTPUT_MODELS_ZOD["SlackUserInfo"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["SlackUserInfo"] },
  "slack:list-channels": { model: "SlackConversationsList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["SlackConversationsList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["SlackConversationsList"] },
  "slack:search-messages": { model: "SlackSearchResultList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["SlackSearchResultList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["SlackSearchResultList"] },
  "slack:send-message-as-user": { model: "SlackSendMessageOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["SlackSendMessageOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["SlackSendMessageOutput"] },
  "slack:update-message-as-user": { model: "SlackUpdateMessageOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["SlackUpdateMessageOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["SlackUpdateMessageOutput"] },
  "google-calendar:create-event": { model: "GoogleCalendarEvent", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleCalendarEvent"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleCalendarEvent"] },
  "google-calendar:delete-event": { model: "GoogleCalendarEventDeleteOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleCalendarEventDeleteOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleCalendarEventDeleteOutput"] },
  "google-calendar:list-calendars": { model: "GoogleCalendarList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleCalendarList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleCalendarList"] },
  "google-calendar:list-events": { model: "GoogleCalendarEventList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleCalendarEventList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleCalendarEventList"] },
  "google-calendar:update-event": { model: "GoogleCalendarEvent", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleCalendarEvent"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleCalendarEvent"] },
  "google-mail:compose-draft": { model: "GmailDraftOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GmailDraftOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GmailDraftOutput"] },
  "google-mail:compose-draft-reply": { model: "GmailReplyDraftOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GmailReplyDraftOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GmailReplyDraftOutput"] },
  "google-mail:delete-message": { model: "GmailDeleteMessageOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GmailDeleteMessageOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GmailDeleteMessageOutput"] },
  "google-mail:get-message": { model: "GmailMessage", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GmailMessage"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GmailMessage"] },
  "google-mail:list-messages": { model: "GmailMessageList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GmailMessageList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GmailMessageList"] },
  "google-mail:modify-message-labels": { model: "GmailMessage", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GmailMessage"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GmailMessage"] },
  "google-mail:send-email": { model: "GmailSendEmailOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GmailSendEmailOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GmailSendEmailOutput"] },
  "google-mail:trash-message": { model: "GmailMessage", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GmailMessage"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GmailMessage"] },
  "google-mail:untrash-message": { model: "GmailMessage", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GmailMessage"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GmailMessage"] },
  "dropbox:copy-file": { model: "DropboxEntry", zodSchema: ACTION_OUTPUT_MODELS_ZOD["DropboxEntry"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["DropboxEntry"] },
  "dropbox:create-folder": { model: "DropboxFolder", zodSchema: ACTION_OUTPUT_MODELS_ZOD["DropboxFolder"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["DropboxFolder"] },
  "dropbox:delete-file": { model: "DropboxDeleteResult", zodSchema: ACTION_OUTPUT_MODELS_ZOD["DropboxDeleteResult"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["DropboxDeleteResult"] },
  "dropbox:fetch-file": { model: "Anonymous_dropbox_action_fetchfile_output", zodSchema: ACTION_OUTPUT_MODELS_ZOD["Anonymous_dropbox_action_fetchfile_output"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["Anonymous_dropbox_action_fetchfile_output"] },
  "dropbox:get-file": { model: "DropboxFile", zodSchema: ACTION_OUTPUT_MODELS_ZOD["DropboxFile"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["DropboxFile"] },
  "dropbox:list-files": { model: "DropboxFileList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["DropboxFileList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["DropboxFileList"] },
  "dropbox:move-file": { model: "DropboxEntry", zodSchema: ACTION_OUTPUT_MODELS_ZOD["DropboxEntry"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["DropboxEntry"] },
  "dropbox:search-files": { model: "DropboxSearchResult", zodSchema: ACTION_OUTPUT_MODELS_ZOD["DropboxSearchResult"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["DropboxSearchResult"] },
  "dropbox:upload-file": { model: "DropboxFile", zodSchema: ACTION_OUTPUT_MODELS_ZOD["DropboxFile"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["DropboxFile"] },
  "notion:create-database": { model: "NotionDatabase", zodSchema: ACTION_OUTPUT_MODELS_ZOD["NotionDatabase"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["NotionDatabase"] },
  "notion:create-page": { model: "NotionPageOrDatabase", zodSchema: ACTION_OUTPUT_MODELS_ZOD["NotionPageOrDatabase"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["NotionPageOrDatabase"] },
  "notion:get-database": { model: "NotionDatabase", zodSchema: ACTION_OUTPUT_MODELS_ZOD["NotionDatabase"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["NotionDatabase"] },
  "notion:get-page": { model: "NotionPageOrDatabase", zodSchema: ACTION_OUTPUT_MODELS_ZOD["NotionPageOrDatabase"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["NotionPageOrDatabase"] },
  "notion:query-database": { model: "NotionQueryDatabaseOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["NotionQueryDatabaseOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["NotionQueryDatabaseOutput"] },
  "notion:search": { model: "NotionSearchOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["NotionSearchOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["NotionSearchOutput"] },
  "notion:update-database": { model: "NotionDatabase", zodSchema: ACTION_OUTPUT_MODELS_ZOD["NotionDatabase"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["NotionDatabase"] },
  "notion:update-page": { model: "NotionPageOrDatabase", zodSchema: ACTION_OUTPUT_MODELS_ZOD["NotionPageOrDatabase"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["NotionPageOrDatabase"] },
  "google-docs:create-document": { model: "GoogleDocsDocument", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleDocsDocument"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleDocsDocument"] },
  "google-docs:get-document": { model: "GoogleDocsDocument", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleDocsDocument"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleDocsDocument"] },
  "google-docs:update-document": { model: "GoogleDocsUpdateDocumentOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleDocsUpdateDocumentOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleDocsUpdateDocumentOutput"] },
  "linear:create-issue": { model: "LinearIssue", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinearIssue"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinearIssue"] },
  "linear:create-project": { model: "LinearProject", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinearProject"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinearProject"] },
  "linear:delete-issue": { model: "LinearDeleteIssueOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinearDeleteIssueOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinearDeleteIssueOutput"] },
  "linear:fetch-models": { model: "ModelResponse", zodSchema: ACTION_OUTPUT_MODELS_ZOD["ModelResponse"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["ModelResponse"] },
  "linear:get-issue": { model: "LinearIssue", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinearIssue"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinearIssue"] },
  "linear:get-project": { model: "LinearProject", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinearProject"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinearProject"] },
  "linear:get-team": { model: "LinearTeam", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinearTeam"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinearTeam"] },
  "linear:list-issues": { model: "LinearIssueList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinearIssueList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinearIssueList"] },
  "linear:list-projects": { model: "LinearProjectList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinearProjectList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinearProjectList"] },
  "linear:list-teams": { model: "LinearTeamList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinearTeamList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinearTeamList"] },
  "linear:update-issue": { model: "LinearIssue", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinearIssue"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinearIssue"] },
  "linear:update-project": { model: "LinearProject", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinearProject"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinearProject"] },
  "google-sheet:create-sheet": { model: "GoogleSheetCreateOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleSheetCreateOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleSheetCreateOutput"] },
  "google-sheet:fetch-spreadsheet": { model: "Spreadsheet", zodSchema: ACTION_OUTPUT_MODELS_ZOD["Spreadsheet"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["Spreadsheet"] },
  "google-sheet:update-sheet": { model: "GoogleSheetUpdateOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleSheetUpdateOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleSheetUpdateOutput"] },
  "google-drive:fetch-document": { model: "Anonymous_googledrive_action_fetchdocument_output", zodSchema: ACTION_OUTPUT_MODELS_ZOD["Anonymous_googledrive_action_fetchdocument_output"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["Anonymous_googledrive_action_fetchdocument_output"] },
  "google-drive:fetch-google-doc": { model: "JSONDocument", zodSchema: ACTION_OUTPUT_MODELS_ZOD["JSONDocument"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["JSONDocument"] },
  "google-drive:fetch-google-sheet": { model: "JSONSpreadsheet", zodSchema: ACTION_OUTPUT_MODELS_ZOD["JSONSpreadsheet"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["JSONSpreadsheet"] },
  "google-drive:folder-content": { model: "FolderContent", zodSchema: ACTION_OUTPUT_MODELS_ZOD["FolderContent"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["FolderContent"] },
  "google-drive:list-documents": { model: "GoogleDriveDocumentList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleDriveDocumentList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleDriveDocumentList"] },
  "google-drive:list-root-folders": { model: "GoogleDriveFolderList", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleDriveFolderList"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleDriveFolderList"] },
  "google-drive:upload-document": { model: "GoogleDocument", zodSchema: ACTION_OUTPUT_MODELS_ZOD["GoogleDocument"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["GoogleDocument"] },
  "linkedin:get-user-profile": { model: "LinkedInUserProfile", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinkedInUserProfile"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinkedInUserProfile"] },
  "linkedin:send-post": { model: "LinkedInPostOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["LinkedInPostOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["LinkedInPostOutput"] },
  "twitter-v2:get-user-profile": { model: "XSocialUserProfile", zodSchema: ACTION_OUTPUT_MODELS_ZOD["XSocialUserProfile"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["XSocialUserProfile"] },
  "twitter-v2:send-post": { model: "XSocialPostOutput", zodSchema: ACTION_OUTPUT_MODELS_ZOD["XSocialPostOutput"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["XSocialPostOutput"] },
};

export const SYNC_OUTPUT_MODELS_KEYED = {
  "slack:messages": { model: "SlackSyncMessage", zodSchema: SYNC_OUTPUT_MODELS_ZOD["SlackSyncMessage"], jsonSchema: SYNC_OUTPUT_MODELS_JSON_SCHEMA["SlackSyncMessage"] },
  "google-calendar:events-fork": { model: "GoogleCalendarEvent", zodSchema: SYNC_OUTPUT_MODELS_ZOD["GoogleCalendarEvent"], jsonSchema: SYNC_OUTPUT_MODELS_JSON_SCHEMA["GoogleCalendarEvent"] },
  "google-mail:emails-fork": { model: "GmailEmail", zodSchema: SYNC_OUTPUT_MODELS_ZOD["GmailEmail"], jsonSchema: SYNC_OUTPUT_MODELS_JSON_SCHEMA["GmailEmail"] },
  "dropbox:files-fork": { model: "DropboxFile", zodSchema: SYNC_OUTPUT_MODELS_ZOD["DropboxFile"], jsonSchema: SYNC_OUTPUT_MODELS_JSON_SCHEMA["DropboxFile"] },
  "google-drive:documents-fork": { model: "Document", zodSchema: SYNC_OUTPUT_MODELS_ZOD["Document"], jsonSchema: SYNC_OUTPUT_MODELS_JSON_SCHEMA["Document"] },
};

export const ACTION_INPUTS_KEYED = {
  "harvest:add-historical-time-entry": "HarvestAddHistoricalTimeEntryInput",
  "harvest:create-client": "HarvestCreateClientInput",
  "harvest:create-project": "HarvestCreateProjectInput",
  "harvest:delete-project": "HarvestProjectInput",
  "harvest:delete-time-entry": "HarvestTimeEntryInput",
  "harvest:get-client": "HarvestClientInput",
  "harvest:get-project": "HarvestProjectInput",
  "harvest:get-time-entry": "HarvestTimeEntryInput",
  "harvest:list-projects": "HarvestProjectsInput",
  "harvest:list-project-tasks": "HarvestProjectTasksInput",
  "harvest:list-tasks": "HarvestTasksInput",
  "harvest:list-time-entries": "HarvestTimeEntriesInput",
  "harvest:restart-timer": "HarvestTimeEntryInput",
  "harvest:start-timer": "HarvestStartTimerInput",
  "harvest:stop-timer": "HarvestTimeEntryInput",
  "harvest:update-time-entry": "HarvestUpdateTimeEntryInput",
  "github:add-pull-request-review-comment": "GithubAddPullRequestReviewCommentInput",
  "github:create-issue": "GithubCreateIssueInput",
  "github:create-organization-repository": "GithubCreateOrganizationRepositoryInput",
  "github:create-pull-request": "GithubCreatePullRequestInput",
  "github:create-pull-request-review": "GithubCreatePullRequestReviewInput",
  "github:create-repository": "GithubCreateRepositoryInput",
  "github:delete-repository": "GithubRepositoryInput",
  "github:get-issue": "GithubIssueInput",
  "github:get-pull-request": "GithubPullRequestInput",
  "github:get-pull-request-comments": "GithubPullRequestInput",
  "github:get-pull-request-files": "GithubPullRequestInput",
  "github:get-pull-request-status": "GithubPullRequestInput",
  "github:get-repository": "GithubRepositoryInput",
  "github:list-branches": "GithubListBranchesInput",
  "github:list-issues": "GithubIssuesInput",
  "github:list-pull-requests": "GithubListPullRequestsInput",
  "github:merge-pull-request": "GithubMergePullRequestInput",
  "github:update-issue": "GithubUpdateIssueInput",
  "github:update-pull-request": "GithubUpdatePullRequestInput",
  "github:update-pull-request-branch": "GithubUpdatePullRequestBranchInput",
  "github:update-repository": "GithubUpdateRepositoryInput",
  "github:write-file": "GithubWriteFileInput",
  "slack:add-reaction-as-user": "SlackAddReactionInput",
  "slack:get-channel-history": "SlackGetChannelHistoryInput",
  "slack:get-message-permalink": "SlackGetPermalinkInput",
  "slack:get-user-info": "SlackGetUserInfoInput",
  "slack:list-channels": "SlackListChannelsInput",
  "slack:search-messages": "SlackSearchMessagesInput",
  "slack:send-message-as-user": "SlackSendMessageInput",
  "slack:update-message-as-user": "SlackUpdateMessageInput",
  "google-calendar:create-event": "GoogleCalendarEventInput",
  "google-calendar:delete-event": "GoogleCalendarEventDeleteInput",
  "google-calendar:list-events": "GoogleCalendarEventsInput",
  "google-calendar:update-event": "GoogleCalendarEventUpdateInput",
  "google-mail:compose-draft": "GmailDraftInput",
  "google-mail:compose-draft-reply": "GmailReplyDraftInput",
  "google-mail:delete-message": "GmailMessageIdInput",
  "google-mail:get-message": "GmailGetMessageInput",
  "google-mail:list-messages": "GmailListMessagesInput",
  "google-mail:modify-message-labels": "GmailModifyMessageLabelsInput",
  "google-mail:send-email": "GmailSendEmailInput",
  "google-mail:trash-message": "GmailMessageIdInput",
  "google-mail:untrash-message": "GmailMessageIdInput",
  "dropbox:copy-file": "DropboxCopyInput",
  "dropbox:create-folder": "DropboxCreateFolderInput",
  "dropbox:delete-file": "DropboxDeleteInput",
  "dropbox:fetch-file": "Anonymous_dropbox_action_fetchfile_input",
  "dropbox:get-file": "DropboxGetFileInput",
  "dropbox:list-files": "DropboxListFilesInput",
  "dropbox:move-file": "DropboxMoveInput",
  "dropbox:search-files": "DropboxSearchInput",
  "dropbox:upload-file": "DropboxUploadFileInput",
  "notion:create-database": "NotionCreateDatabaseInput",
  "notion:create-page": "NotionCreatePageInput",
  "notion:get-database": "NotionGetDatabaseInput",
  "notion:get-page": "NotionGetPageInput",
  "notion:query-database": "NotionQueryDatabaseInput",
  "notion:search": "NotionSearchInput",
  "notion:update-database": "NotionUpdateDatabaseInput",
  "notion:update-page": "NotionUpdatePageInput",
  "google-docs:create-document": "GoogleDocsCreateDocumentInput",
  "google-docs:get-document": "GoogleDocsGetDocumentInput",
  "google-docs:update-document": "GoogleDocsUpdateDocumentInput",
  "linear:create-issue": "LinearCreateIssueInput",
  "linear:create-project": "LinearCreateProjectInput",
  "linear:delete-issue": "LinearIssueInput",
  "linear:get-issue": "LinearIssueInput",
  "linear:get-project": "LinearProjectInput",
  "linear:get-team": "LinearTeamInput",
  "linear:list-issues": "LinearIssuesInput",
  "linear:list-projects": "LinearProjectsInput",
  "linear:list-teams": "LinearTeamsInput",
  "linear:update-issue": "LinearUpdateIssueInput",
  "linear:update-project": "LinearUpdateProjectInput",
  "google-sheet:create-sheet": "GoogleSheetCreateInput",
  "google-sheet:fetch-spreadsheet": "SpreadsheetId",
  "google-sheet:update-sheet": "GoogleSheetUpdateInput",
  "google-drive:fetch-document": "IdEntity",
  "google-drive:fetch-google-doc": "IdEntity",
  "google-drive:fetch-google-sheet": "IdEntity",
  "google-drive:folder-content": "FolderContentInput",
  "google-drive:list-documents": "ListDocumentsInput",
  "google-drive:upload-document": "UploadFileInput",
  "linkedin:send-post": "LinkedInPostInput",
  "twitter-v2:send-post": "XSocialPostInput",
};

export const ACTION_OUTPUTS_KEYED = {
  "harvest:add-historical-time-entry": "HarvestTimeEntry",
  "harvest:create-client": "HarvestClient",
  "harvest:create-project": "HarvestProject",
  "harvest:delete-project": "HarvestDeleteProjectOutput",
  "harvest:delete-time-entry": "HarvestDeleteTimeEntryOutput",
  "harvest:get-client": "HarvestClient",
  "harvest:get-project": "HarvestProject",
  "harvest:get-time-entry": "HarvestTimeEntry",
  "harvest:list-clients": "HarvestClientList",
  "harvest:list-projects": "HarvestProjectList",
  "harvest:list-project-tasks": "HarvestProjectTaskList",
  "harvest:list-tasks": "HarvestTaskList",
  "harvest:list-time-entries": "HarvestTimeEntryList",
  "harvest:restart-timer": "HarvestTimeEntry",
  "harvest:start-timer": "HarvestTimeEntry",
  "harvest:stop-timer": "HarvestTimeEntry",
  "harvest:update-time-entry": "HarvestTimeEntry",
  "github:add-pull-request-review-comment": "GithubPullRequestComment",
  "github:create-issue": "GithubIssue",
  "github:create-organization-repository": "GithubRepository",
  "github:create-pull-request": "GithubPullRequest",
  "github:create-pull-request-review": "GithubPullRequestReview",
  "github:create-repository": "GithubRepository",
  "github:delete-repository": "GithubDeleteRepositoryOutput",
  "github:get-issue": "GithubIssue",
  "github:get-pull-request": "GithubPullRequest",
  "github:get-pull-request-comments": "GithubPullRequestCommentList",
  "github:get-pull-request-files": "GithubPullRequestFileList",
  "github:get-pull-request-status": "GithubCombinedStatus",
  "github:get-repository": "GithubRepository",
  "github:list-branches": "GithubBranchList",
  "github:list-issues": "GithubIssueList",
  "github:list-pull-requests": "GithubPullRequestList",
  "github:list-repositories": "GithubRepositoryList",
  "github:merge-pull-request": "GithubMergeResult",
  "github:update-issue": "GithubIssue",
  "github:update-pull-request": "GithubPullRequest",
  "github:update-pull-request-branch": "GithubBranchUpdateResult",
  "github:update-repository": "GithubRepository",
  "github:write-file": "GithubWriteFileActionResult",
  "slack:add-reaction-as-user": "SlackReactionOutput",
  "slack:get-channel-history": "SlackMessageList",
  "slack:get-message-permalink": "SlackPermalinkOutput",
  "slack:get-user-info": "SlackUserInfo",
  "slack:list-channels": "SlackConversationsList",
  "slack:search-messages": "SlackSearchResultList",
  "slack:send-message-as-user": "SlackSendMessageOutput",
  "slack:update-message-as-user": "SlackUpdateMessageOutput",
  "google-calendar:create-event": "GoogleCalendarEvent",
  "google-calendar:delete-event": "GoogleCalendarEventDeleteOutput",
  "google-calendar:list-calendars": "GoogleCalendarList",
  "google-calendar:list-events": "GoogleCalendarEventList",
  "google-calendar:update-event": "GoogleCalendarEvent",
  "google-mail:compose-draft": "GmailDraftOutput",
  "google-mail:compose-draft-reply": "GmailReplyDraftOutput",
  "google-mail:delete-message": "GmailDeleteMessageOutput",
  "google-mail:get-message": "GmailMessage",
  "google-mail:list-messages": "GmailMessageList",
  "google-mail:modify-message-labels": "GmailMessage",
  "google-mail:send-email": "GmailSendEmailOutput",
  "google-mail:trash-message": "GmailMessage",
  "google-mail:untrash-message": "GmailMessage",
  "dropbox:copy-file": "DropboxEntry",
  "dropbox:create-folder": "DropboxFolder",
  "dropbox:delete-file": "DropboxDeleteResult",
  "dropbox:fetch-file": "Anonymous_dropbox_action_fetchfile_output",
  "dropbox:get-file": "DropboxFile",
  "dropbox:list-files": "DropboxFileList",
  "dropbox:move-file": "DropboxEntry",
  "dropbox:search-files": "DropboxSearchResult",
  "dropbox:upload-file": "DropboxFile",
  "notion:create-database": "NotionDatabase",
  "notion:create-page": "NotionPageOrDatabase",
  "notion:get-database": "NotionDatabase",
  "notion:get-page": "NotionPageOrDatabase",
  "notion:query-database": "NotionQueryDatabaseOutput",
  "notion:search": "NotionSearchOutput",
  "notion:update-database": "NotionDatabase",
  "notion:update-page": "NotionPageOrDatabase",
  "google-docs:create-document": "GoogleDocsDocument",
  "google-docs:get-document": "GoogleDocsDocument",
  "google-docs:update-document": "GoogleDocsUpdateDocumentOutput",
  "linear:create-issue": "LinearIssue",
  "linear:create-project": "LinearProject",
  "linear:delete-issue": "LinearDeleteIssueOutput",
  "linear:fetch-models": "ModelResponse",
  "linear:get-issue": "LinearIssue",
  "linear:get-project": "LinearProject",
  "linear:get-team": "LinearTeam",
  "linear:list-issues": "LinearIssueList",
  "linear:list-projects": "LinearProjectList",
  "linear:list-teams": "LinearTeamList",
  "linear:update-issue": "LinearIssue",
  "linear:update-project": "LinearProject",
  "google-sheet:create-sheet": "GoogleSheetCreateOutput",
  "google-sheet:fetch-spreadsheet": "Spreadsheet",
  "google-sheet:update-sheet": "GoogleSheetUpdateOutput",
  "google-drive:fetch-document": "Anonymous_googledrive_action_fetchdocument_output",
  "google-drive:fetch-google-doc": "JSONDocument",
  "google-drive:fetch-google-sheet": "JSONSpreadsheet",
  "google-drive:folder-content": "FolderContent",
  "google-drive:list-documents": "GoogleDriveDocumentList",
  "google-drive:list-root-folders": "GoogleDriveFolderList",
  "google-drive:upload-document": "GoogleDocument",
  "linkedin:get-user-profile": "LinkedInUserProfile",
  "linkedin:send-post": "LinkedInPostOutput",
  "twitter-v2:get-user-profile": "XSocialUserProfile",
  "twitter-v2:send-post": "XSocialPostOutput",
};

export const SYNC_OUTPUTS_KEYED = {
  "slack:messages": "SlackSyncMessage",
  "google-calendar:events-fork": "GoogleCalendarEvent",
  "google-mail:emails-fork": "GmailEmail",
  "dropbox:files-fork": "DropboxFile",
  "google-drive:documents-fork": "Document",
};

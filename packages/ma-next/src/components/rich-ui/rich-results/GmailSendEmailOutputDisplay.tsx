import React from 'react';
import { Mail, Send, Link, Tag, MoreHorizontal, ExternalLink } from 'lucide-react';
import { GmailSendEmailOutput } from 'src/config/nangoModels';
import { ContextMenu, ContextMenuItem } from '../common/ContextMenu';
import { DropdownMenu } from '../common/DropdownMenu';

type GmailSendEmailOutputDisplayProps = {
  output: GmailSendEmailOutput;
};

/**
 * Renders a rich display of a Gmail send email result
 */
function GmailSendEmailOutputDisplay({ output }: GmailSendEmailOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <Mail className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No email data available</p>
      </div>
    );
  }

  const menuItems: ContextMenuItem[] = [
    {
      label: 'View in Gmail Sent Folder',
      href: 'https://mail.google.com/mail/u/0/#sent',
      icon: <ExternalLink className="w-4 h-4 mr-2" />,
    },
  ];

  return (
    <ContextMenu items={menuItems}>
      <div>
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Send className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Email Sent Successfully
              </h3>
            </div>
            <DropdownMenu
              trigger={
                <button
                  className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  title="More options"
                >
                  <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              }
              items={menuItems}
            />
          </div>
        </div>

      <div className="p-5 space-y-4">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3">
            <Send className="w-5 h-5 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h4 className="text-base font-medium text-gray-900 dark:text-white">Email Sent</h4>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-0.5">
              The email has been sent successfully
            </p>
          </div>
        </div>

        <div className="mt-4 space-y-2">
          <div className="flex items-start">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-32">
              Message ID:
            </div>
            <div className="text-sm text-gray-800 dark:text-gray-200 break-all">{output.id}</div>
          </div>

          <div className="flex items-start">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-32">
              Thread ID:
            </div>
            <div className="text-sm text-gray-800 dark:text-gray-200 break-all">
              {output.threadId}
            </div>
          </div>

          {output.labelIds && output.labelIds.length > 0 && (
            <div className="flex items-start">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-32">
                Labels:
              </div>
              <div className="flex flex-wrap gap-1.5">
                {output.labelIds.map((label, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                  >
                    <Tag className="w-3 h-3 mr-1" />
                    {label.replace('CATEGORY_', '').toLowerCase()}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

      </div>
    </div>
    </ContextMenu>
  );
}

export { GmailSendEmailOutputDisplay };

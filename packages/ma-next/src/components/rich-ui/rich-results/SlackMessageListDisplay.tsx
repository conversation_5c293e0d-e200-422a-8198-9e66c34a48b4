import React, { useMemo, useState } from 'react';
import { MessageSquare, User, Clock, Paperclip, Download, MoreHorizontal, ExternalLink } from 'lucide-react';
import { format } from 'date-fns';
import { SlackMessageList, SlackUserInfo, SlackFile } from 'src/config/nangoModels';
import { slackUsersStore } from 'components/rich-ui/rich-results/shared/slackUsers';
import { DropdownMenu } from '../common/DropdownMenu';
import { ContextMenuItem } from '../common/ContextMenu';

// Extract all user IDs mentioned in a Slack formatted text string
function extractUserIds(text: string): string[] {
  const ids: string[] = [];
  const regex = /<@([A-Z0-9]+)(?:\|[^>]+)?>/g;
  let match;
  while ((match = regex.exec(text))) {
    ids.push(match[1]);
  }
  return ids;
}

// Replace user mention IDs in a Slack text string with display names
function replaceUserMentions(text: string, userMap: Record<string, SlackUserInfo>): string {
  return text.replace(/<@([A-Z0-9]+)(?:\|[^>]+)?>/g, (_full, id) => {
    const info = userMap[id];
    const name = info?.profile?.display_name || info?.profile?.real_name || info?.name || id;
    return `@${name}`;
  });
}

interface SlackMessageListDisplayProps {
  output: SlackMessageList;
}

/**
 * Renders a rich display of Slack channel message history
 */
function SlackMessageListDisplay({ output }: SlackMessageListDisplayProps) {
  const data = output;

  const idsFromUserField =
    data?.messages?.map(m => m.user).filter((id): id is string => Boolean(id)) || [];
  const idsFromText = data?.messages?.flatMap(m => (m.text ? extractUserIds(m.text) : [])) || [];
  const uniqueIds = useMemo(
    () => Array.from(new Set([...idsFromUserField, ...idsFromText])),
    [output]
  );

  const users = slackUsersStore.useSlackUsers(uniqueIds);

  // Check if we have valid data
  if (!data || !data.messages || data.messages.length === 0) {
    return (
      <div className="p-6 text-center">
        <MessageSquare className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No messages found</p>
      </div>
    );
  }

  const messages = data.messages;
  const hasMore = data.has_more;
  const nextCursor = data.response_metadata?.next_cursor;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Slack Messages ({messages.length})
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {messages.map((message, index) => {
          const userInfo = message.user ? users[message.user] : undefined;
          const displayName =
            userInfo?.profile?.display_name ||
            userInfo?.profile?.real_name ||
            userInfo?.name ||
            message.user;
          const avatar = userInfo?.profile?.image_512 || userInfo?.profile?.image_original;
          // Format timestamp
          let formattedTime = '';
          try {
            // Slack timestamps are in seconds, JS Date expects milliseconds
            const timestamp = parseFloat(message.ts) * 1000;
            formattedTime = format(new Date(timestamp), 'MMM d, yyyy h:mm a');
          } catch (e) {
            formattedTime = message.ts;
          }

          return (
            <div
              key={message.ts || index}
              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  {avatar ? (
                    <img src={avatar} alt={displayName} className="w-9 h-9 rounded-full" />
                  ) : (
                    <div className="w-9 h-9 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                      <User className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                    </div>
                  )}
                </div>
                <div className="min-w-0 flex-1">
                  {/* User and timestamp */}
                  <div className="flex items-center mb-1">
                    {displayName && (
                      <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mr-2">
                        {displayName}
                      </div>
                    )}
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <Clock className="w-3 h-3 mr-1" />
                      <span>{formattedTime}</span>
                    </div>
                    {message.thread_ts && message.reply_count && (
                      <div className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                        {message.reply_count} {message.reply_count === 1 ? 'reply' : 'replies'}
                      </div>
                    )}
                  </div>

                  {/* Message text */}
                  <p className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line">
                    {replaceUserMentions(message.text, users)}
                  </p>

                  {/* File attachments */}
                  {message.files && message.files.length > 0 && (
                    <SlackFileAttachments files={message.files} />
                  )}

                  {/* Thread indicator */}
                  {message.thread_ts && !message.reply_count && (
                    <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">In thread</div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {hasMore && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            More messages available. {nextCursor ? '' : ''}
          </p>
        </div>
      )}
    </div>
  );
}

// Component for displaying file attachments
interface SlackFileAttachmentsProps {
  files: SlackFile[];
}

function SlackFileAttachments({ files }: SlackFileAttachmentsProps) {
  const [authRequired, setAuthRequired] = useState<Record<string, boolean>>({});

  // Format file size
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '';
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  // Get the best available thumbnail
  const getThumbnailUrl = (file: SlackFile): string | null => {
    // Prefer larger thumbnails for better quality
    return (
      file.thumb_360 ||
      file.thumb_160 ||
      file.thumb_80 ||
      file.thumb_64 ||
      null
    );
  };

  // Check if file is an image
  const isImage = (file: SlackFile): boolean => {
    return file.mimetype?.startsWith('image/') || false;
  };

  // Handle thumbnail load error (indicates auth required)
  const handleThumbnailError = (fileId: string) => {
    setAuthRequired(prev => ({ ...prev, [fileId]: true }));
  };

  // Handle file download
  const handleDownload = async (file: SlackFile) => {
    if (authRequired[file.id]) {
      // Use backend proxy for authenticated download
      // TODO: Implement backend download endpoint call
      console.log('Download via backend for file:', file.id);
    } else {
      // Try direct download
      if (file.url_private_download) {
        window.open(file.url_private_download, '_blank');
      }
    }
  };

  // Handle file view
  const handleView = async (file: SlackFile) => {
    if (authRequired[file.id]) {
      // Use backend proxy for authenticated view
      // TODO: Implement backend view endpoint call
      console.log('View via backend for file:', file.id);
    } else {
      // Try direct view
      if (file.url_private) {
        window.open(file.url_private, '_blank');
      }
    }
  };

  return (
    <div className="mt-3 space-y-2">
      {files.map((file) => {
        const thumbnailUrl = getThumbnailUrl(file);
        const isImg = isImage(file);

        const menuItems: ContextMenuItem[] = [
          {
            label: 'View in Slack',
            onClick: () => handleView(file),
            icon: <ExternalLink className="w-4 h-4 mr-2" />,
          },
          {
            label: 'Download',
            onClick: () => handleDownload(file),
            icon: <Download className="w-4 h-4 mr-2" />,
          },
        ];

        return (
          <div
            key={file.id}
            className="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
          >
            {/* Thumbnail or file icon */}
            <div className="flex-shrink-0 mr-3">
              {isImg && thumbnailUrl ? (
                <img
                  src={thumbnailUrl}
                  alt={file.name || 'File thumbnail'}
                  className="w-12 h-12 rounded object-cover"
                  onError={() => handleThumbnailError(file.id)}
                />
              ) : (
                <div className="w-12 h-12 rounded bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                  <Paperclip className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
              )}
            </div>

            {/* File info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {file.name || file.title || 'Untitled file'}
                </h4>

                {/* Three-dot menu */}
                <DropdownMenu
                  trigger={
                    <button
                      className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ml-2"
                      title="More options"
                    >
                      <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                    </button>
                  }
                  items={menuItems}
                />
              </div>

              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
                {file.pretty_type && <span>{file.pretty_type}</span>}
                {file.size && (
                  <span className={file.pretty_type ? 'ml-2' : ''}>
                    {formatFileSize(file.size)}
                  </span>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}

export { SlackMessageListDisplay };

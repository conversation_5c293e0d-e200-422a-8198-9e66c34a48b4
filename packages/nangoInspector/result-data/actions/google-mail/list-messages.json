{"input": {"maxResults": 5, "q": "is:unread", "labelIds": ["INBOX"]}, "output": {"messages": [{"id": "19720218e6b47160", "threadId": "19720218e6b47160", "labelIds": ["UNREAD", "CATEGORY_SOCIAL", "INBOX"], "snippet": "Here's what's happened since you were last on LinkedIn", "subject": "Your weekly updates from LinkedIn", "date": "Fri, 30 May 2025 00:39:19 -0700 (PDT)"}, {"id": "19720218e6b47161", "threadId": "19720218e6b47161", "labelIds": ["UNREAD", "INBOX"], "snippet": "Welcome to your new GitHub repository! Here's how to get started with your first commit.", "subject": "Welcome to GitHub - Get Started Guide", "date": "Thu, 29 May 2025 15:22:33 -0700 (PDT)"}, {"id": "19720218e6b47162", "threadId": "19720218e6b47162", "labelIds": ["UNREAD", "CATEGORY_PROMOTIONS", "INBOX"], "snippet": "Your Figma Pro trial expires in 3 days. Upgrade now to continue using advanced features.", "subject": "Your Figma Pro trial is ending soon", "date": "Thu, 29 May 2025 10:15:42 -0700 (PDT)"}, {"id": "19720218e6b47163", "threadId": "19720218e6b47163", "labelIds": ["UNREAD", "INBOX"], "snippet": "Security alert: We detected a new sign-in to your account from a new device in San Francisco.", "subject": "Security Alert - New sign-in detected", "date": "Wed, 28 May 2025 18:45:12 -0700 (PDT)"}, {"id": "19720218e6b47164", "threadId": "19720218e6b47164", "labelIds": ["UNREAD", "CATEGORY_UPDATES", "INBOX"], "snippet": "Your monthly usage report for MakeAgent is ready. You processed 1,247 automation requests this month.", "subject": "MakeAgent Monthly Usage Report - May 2025", "date": "Wed, 28 May 2025 09:30:00 -0700 (PDT)"}], "nextPageToken": "14509119518950486154"}, "error": null, "success": true}
import { useState, useEffect } from 'react';
import { Play, Code, FileText, Loader2, Database } from 'lucide-react';
import { ActionDefinition } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { getActionInputDefinition, loadSampleData } from '@/lib/actionData';
import { ACTION_INPUT_MODELS_JSON_SCHEMA } from '../../../emcpe-server/src/constants';
import { RichParameterDisplay } from '../../../ma-next/src/components/rich-ui/RichParameterDisplay';
import { RichResultDisplay } from '../../../ma-next/src/components/rich-ui/RichResultDisplay';

interface ActionPanelProps {
  action: ActionDefinition | null;
  onExecute: (provider: string, action: string, params: Record<string, any>) => Promise<void>;
  isExecuting: boolean;
  lastResult: any;
}

export function ActionPanel({ action, onExecute, isExecuting, lastResult }: ActionPanelProps) {
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [sampleData, setSampleData] = useState<{ input?: any; output?: any } | null>(null);
  const [loadingSample, setLoadingSample] = useState(false);
  const [parametersAccordionValue, setParametersAccordionValue] = useState<string>('parameters');
  const [jsonText, setJsonText] = useState<string>('{}');
  const [isValidJson, setIsValidJson] = useState<boolean>(true);

  // Collapse parameters section after execution
  useEffect(() => {
    if (lastResult && !isExecuting) {
      setParametersAccordionValue('');
    }
  }, [lastResult, isExecuting]);

  useEffect(() => {
    setParametersAccordionValue('parameters');
  }, [action]);

  // Load sample data when action changes
  useEffect(() => {
    if (action) {
      setLoadingSample(true);
      loadSampleData(action.provider, action.action)
        .then(data => {
          setSampleData(data);
          // Pre-populate parameters with sample input data if available
          if (data?.input) {
            setParameters(data.input);
            setJsonText(JSON.stringify(data.input, null, 2));
            setIsValidJson(true);
          }
        })
        .finally(() => setLoadingSample(false));
    } else {
      setSampleData(null);
      setParameters({});
      setJsonText('{}');
      setIsValidJson(true);
    }
  }, [action]);

  if (!action) {
    return (
      <Card className="h-full">
        <CardContent className="flex flex-col items-center justify-center py-12 text-center h-full">
          <Code className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Select an Action</h3>
          <p className="text-muted-foreground">
            Choose an action from the provider list to view its details and execute it.
          </p>
        </CardContent>
      </Card>
    );
  }

  const handleParameterChange = (key: string, value: any) => {
    setParameters(prev => {
      const newParams = {
        ...prev,
        [key]: value,
      };
      // Update JSON text to reflect parameter changes
      setJsonText(JSON.stringify(newParams, null, 2));
      setIsValidJson(true);
      return newParams;
    });
  };

  const handleJsonTextChange = (text: string) => {
    setJsonText(text);

    try {
      const parsed = JSON.parse(text);
      setParameters(parsed);
      setIsValidJson(true);
    } catch {
      setIsValidJson(false);
      // Don't update parameters if JSON is invalid
    }
  };

  const handleExecute = async () => {
    await onExecute(action.provider, action.action, parameters);
  };

  const getActionParameters = () => {
    if (!action) return [];

    const inputDef = getActionInputDefinition(action.provider, action.action);
    if (!inputDef?.model) return [];

    const schema = ACTION_INPUT_MODELS_JSON_SCHEMA[inputDef.model];
    if (!schema?.properties) return [];

    return Object.entries(schema.properties).map(([name, prop]: [string, any]) => ({
      name,
      type: prop.type || 'string',
      required: schema.required?.includes(name) || false,
      description: prop.description || '',
    }));
  };

  const renderParameterInput = (param: {
    name: string;
    type: string;
    required: boolean;
    description: string;
  }) => {
    const value = parameters[param.name] || '';

    if (
      param.name.includes('body') ||
      param.name.includes('content') ||
      param.name.includes('description') ||
      param.name.includes('attachment') ||
      param.type === 'object'
    ) {
      return (
        <Textarea
          placeholder={`Enter ${param.name}...`}
          value={typeof value === 'object' ? JSON.stringify(value, null, 2) : value}
          onChange={e => {
            try {
              const parsed = JSON.parse(e.target.value);
              handleParameterChange(param.name, parsed);
            } catch {
              handleParameterChange(param.name, e.target.value);
            }
          }}
          className="min-h-[100px] font-mono text-xs"
        />
      );
    }

    if (param.type === 'number') {
      return (
        <Input
          type="number"
          placeholder={`Enter ${param.name}...`}
          value={value}
          onChange={e => handleParameterChange(param.name, parseFloat(e.target.value) || 0)}
        />
      );
    }

    return (
      <Input
        placeholder={`Enter ${param.name}...`}
        value={value}
        onChange={e => handleParameterChange(param.name, e.target.value)}
      />
    );
  };

  const actionParams = getActionParameters();

  return (
    <div className="space-y-6">
      {/* Action Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>
              {action.provider}:{action.action}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {action.description && (
            <p className="text-sm text-muted-foreground">{action.description}</p>
          )}

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Provider:</span>
              <span className="ml-2 text-muted-foreground">{action.provider}</span>
            </div>
            <div>
              <span className="font-medium">Output Model:</span>
              <span className="ml-2 text-muted-foreground">{action.model || 'None'}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Parameters */}
      <Accordion
        type="single"
        collapsible
        value={parametersAccordionValue}
        onValueChange={setParametersAccordionValue}
      >
        <AccordionItem value="parameters">
          <Card>
            <AccordionTrigger className="hover:no-underline">
              <CardHeader className="py-2">
                <CardTitle>Parameters</CardTitle>
              </CardHeader>
            </AccordionTrigger>
            <AccordionContent>
              <CardContent className="space-y-4 pt-0">
                {loadingSample && (
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Loading sample data...</span>
                  </div>
                )}

                {actionParams.length > 0 ? (
                  actionParams.map(param => (
                    <div key={param.name} className="space-y-2">
                      <label className="text-sm font-medium">
                        {param.name}
                        {param.required && <span className="text-red-500 ml-1">*</span>}
                        {param.description && (
                          <span className="text-xs text-muted-foreground ml-2">
                            ({param.description})
                          </span>
                        )}
                      </label>
                      {renderParameterInput(param)}
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-muted-foreground">
                    No parameters required for this action.
                  </div>
                )}

                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom JSON Parameters</label>
                  <Textarea
                    placeholder='{"key": "value"}'
                    value={jsonText}
                    onChange={e => handleJsonTextChange(e.target.value)}
                    className={`min-h-[100px] font-mono text-xs ${
                      !isValidJson ? 'border-red-500 ring-1 ring-red-500' : ''
                    }`}
                  />
                  {!isValidJson && (
                    <p className="text-xs text-red-500">Invalid JSON format</p>
                  )}
                </div>

                <Separator />

                {/* Rich Parameter Display */}
                {action && RichParameterDisplay.canDisplay(action.provider, action.action) ? (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Rich Parameter Preview</h4>
                    <div className="border rounded-lg p-1">
                      <RichParameterDisplay
                        providerKey={action.provider}
                        actionKey={action.action}
                        actionParameters={parameters}
                      />
                    </div>
                  </div>
                ) : null}

                <Button onClick={handleExecute} disabled={isExecuting} className="w-full">
                  {isExecuting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Executing...
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" />
                      Execute Action
                    </>
                  )}
                </Button>
              </CardContent>
            </AccordionContent>
          </Card>
        </AccordionItem>
      </Accordion>

      {/* Sample Output */}
      {sampleData?.output && !lastResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-4 w-4" />
              <span>Sample Output</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Rich Result Display */}
            {action && (
              <div>
                <h4 className="text-sm font-medium mb-2">Rich Result Preview</h4>
                <RichResultDisplay
                  result={sampleData.output}
                  providerKey={action.provider}
                  actionKey={action.action}
                  actionParameters={sampleData.input}
                  context={action.action}
                />
              </div>
            )}

            <Separator />

            {/* Raw JSON */}
            <div>
              <h4 className="text-sm font-medium mb-2">Raw JSON</h4>
              <pre className="bg-muted p-4 rounded-md text-xs overflow-auto max-h-96">
                {JSON.stringify(sampleData.output, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {lastResult && (
        <Card>
          <CardHeader>
            <CardTitle>Last Result</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Rich Result Display */}
            {action && lastResult.success && lastResult.result && (
              <div>
                <h4 className="text-sm font-medium mb-8">Rich Result Preview</h4>
                <RichResultDisplay
                  result={lastResult.result}
                  providerKey={action.provider}
                  actionKey={action.action}
                  actionParameters={parameters}
                  context={action.action}
                />
              </div>
            )}

            <Separator />

            {/* Raw JSON */}
            <div>
              <h4 className="text-sm font-medium mb-2">Raw JSON</h4>
              <pre className="bg-muted p-4 rounded-md text-xs overflow-auto max-h-96">
                {JSON.stringify(lastResult, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

import type { NangoAction, SlackDownloadFileInput, SlackDownloadFileOutput } from '../../models';

interface SlackFileInfoResponse {
  ok: boolean;
  file?: {
    id: string;
    url_private?: string;
    url_private_download?: string;
  };
  error?: string;
}

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackDownloadFileInput
): Promise<SlackDownloadFileOutput | ErrorResponse> {
  if (!input.file_id) {
    return {
      error: { status: 400, message: 'File ID is required for downloading a Slack file.' },
    };
  }

  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const config = {
      method: 'GET' as const,
      baseUrlOverride: 'https://slack.com/api',
      endpoint: `files.info?file=${input.file_id}`,
      headers: {
        Authorization: `Bearer ${userToken}`,
        'Content-Type': 'application/json; charset=utf-8',
      },
      retries: 3,
    };

    const response = await nango.proxy<SlackFileInfoResponse>(config);

    if (!response.data.ok) {
      const errorMessage = `Slack API error: ${response.data.error || 'Unknown error getting file info'}`;
      await nango.log(errorMessage);
      const status = response.status ?? 400;
      return { error: { status: status, message: errorMessage } };
    }

    if (!response.data.file?.url_private_download) {
      const errorMessage = 'Slack API response missing file download URL.';
      await nango.log(errorMessage);
      return { error: { status: 500, message: errorMessage } };
    }

    const result: SlackDownloadFileOutput = {
      ok: response.data.ok,
      file_url: response.data.file.url_private_download,
    };
    return result;
  } catch (error: any) {
    await nango.log(`Error getting Slack file info: ${error.message}`);
    const errorMessage =
      error.response?.data?.error || error.message || 'Unknown error getting Slack file info';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      await nango.log('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      await nango.log('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    await nango.log(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}

import type {
  NangoSync,
  SlackConversation,
  SlackSyncMessage,
  SlackMessage,
  SlackFile,
  UrlAccessibleFile,
} from '../../models';

export interface SlackScopeSyncMetadata {
  conversations?: string[];
}

interface Metadata extends SlackScopeSyncMetadata {
  cursors?: Record<string, string>;
}

export default async function fetchData(nango: NangoSync) {
  const metadata = (await nango.getMetadata<Metadata>()) || {};
  const cursors = metadata.cursors || {};

  const userToken = await getUserToken(nango);
  if (!userToken) {
    await nango.log('User token missing for Slack sync.');
    return;
  }

  let channels = await listAllChannels(nango, userToken);
  if (metadata.conversations && metadata.conversations.length) {
    channels = channels.filter(c => metadata.conversations!.includes(c.id));
  }

  for (const channel of channels) {
    const lastCursor = cursors[channel.id] || '0';
    const latest = await syncChannel(nango, channel, lastCursor, userToken);
    if (latest !== lastCursor) {
      cursors[channel.id] = latest;
    }
  }

  await nango.setMetadata({ cursors });
}

async function listAllChannels(nango: NangoSync, token: string): Promise<SlackConversation[]> {
  let nextCursor = '';
  const channels: SlackConversation[] = [];

  do {
    const res = await nango.get({
      endpoint: 'conversations.list',
      params: { limit: '200', cursor: nextCursor, types: 'public_channel,private_channel,im,mpim' },
      headers: { Authorization: `Bearer ${token}` },
      retries: 10,
    });

    if (!res.data.ok) {
      await nango.log(`Slack API error listing channels: ${JSON.stringify(res.data)}`);
      break;
    }

    channels.push(...res.data.channels);
    nextCursor = res.data.response_metadata?.next_cursor || '';
  } while (nextCursor);

  return channels;
}

async function syncChannel(
  nango: NangoSync,
  channel: SlackConversation,
  since: string,
  token: string
): Promise<string> {
  let nextCursor = '';
  let latestTs = since;
  do {
    const res = await nango.get({
      endpoint: 'conversations.history',
      params: { channel: channel.id, limit: '200', cursor: nextCursor, oldest: since },
      headers: { Authorization: `Bearer ${token}` },
      retries: 10,
    });

    if (!res.data.ok) {
      await nango.log(`Slack API error history for ${channel.id}: ${JSON.stringify(res.data)}`);
      break;
    }

    const records: SlackSyncMessage[] = [];
    const messages: SlackMessage[] = res.data.messages || [];

    for (const msg of messages) {
      const messageWithFiles: SlackMessage & { files?: (SlackFile & { accessible?: UrlAccessibleFile })[] } = { ...msg };
      if (msg.files && msg.files.length) {
        messageWithFiles.files = msg.files.map(f => ({
          ...f,
          accessible: {
            url: f.url_private_download || f.url_private || '',
            authentication: {
              providerKey: 'slack',
              connectionId: nango.connectionId,
            },
          },
        }));
      }

      records.push({
        id: msg.ts,
        channel_id: channel.id,
        message: messageWithFiles,
      });
      if (msg.thread_ts && msg.thread_ts === msg.ts && msg.reply_count) {
        const replies = await getThreadReplies(nango, channel.id, msg.thread_ts, since, token);
        for (const rep of replies) {
          records.push({
            id: rep.ts,
            channel_id: channel.id,
            message: rep,
          });
        }
      }
      if (parseFloat(msg.ts) > parseFloat(latestTs)) {
        latestTs = msg.ts;
      }
    }

    if (records.length) {
      await nango.batchSave(records, 'SlackSyncMessage');
    }

    nextCursor = res.data.response_metadata?.next_cursor || '';
  } while (nextCursor);

  return latestTs;
}

async function getThreadReplies(
  nango: NangoSync,
  channelId: string,
  threadTs: string,
  since: string,
  token: string
): Promise<SlackMessage[]> {
  let nextCursor = '';
  const replies: SlackMessage[] = [];

  do {
    const res = await nango.get({
      endpoint: 'conversations.replies',
      params: { channel: channelId, ts: threadTs, cursor: nextCursor, oldest: since },
      headers: { Authorization: `Bearer ${token}` },
      retries: 10,
    });

    if (!res.data.ok) {
      await nango.log(`Slack API error replies for ${channelId}: ${JSON.stringify(res.data)}`);
      break;
    }

    const msgs = res.data.messages || [];
    for (const m of msgs) {
      if (m.ts !== threadTs) {
        replies.push(m);
      }
    }
    nextCursor = res.data.response_metadata?.next_cursor || '';
  } while (nextCursor);

  return replies;
}

async function getUserToken(nango: NangoSync): Promise<string | null> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      return null;
    }
    const raw = connection.credentials['raw'] as any;
    const userToken = raw?.['authed_user']?.['access_token'];
    return typeof userToken === 'string' ? userToken : null;
  } catch {
    return null;
  }
}
